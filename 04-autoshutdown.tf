resource "azurerm_dev_test_global_vm_shutdown_schedule" "auto_shutdown" {
  count = var.auto_shutdown != null ? 1 : 0
  
  virtual_machine_id = azurerm_windows_virtual_machine.vm.id
  location           = var.conventions.region
  enabled            = var.auto_shutdown.enabled

  daily_recurrence_time = var.auto_shutdown.daily_recurrence_time
  timezone              = var.auto_shutdown.timezone

  tags = local.tags

  notification_settings {
    enabled         = var.auto_shutdown.notification_enabled
    time_in_minutes = var.auto_shutdown.notification_time_in_mins
    webhook_url     = var.auto_shutdown.notification_webhook_url
    email           = var.auto_shutdown.notification_email
  }
}




