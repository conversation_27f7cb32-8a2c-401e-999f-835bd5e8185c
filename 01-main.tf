module "aasp" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  #checkov:skip=CKV_AZURE_225: Zone redundancy requires a capacity request and also it is not a hard requirement to deploy an application into zones for lower level environments
  count                     = var.azurerm_service_plan_sku_name != null ? 1 : 0
  source                    = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-appservice?ref=v2.1.0"
  conventions               = var.conventions
  resource_group_name       = var.resource_group_name
  resource_name_suffix      = local.service_plan_name
  os_type                   = "Windows"
  sku_name                  = var.azurerm_service_plan_sku_name
  worker_count              = var.worker_count
}

resource "azurerm_windows_web_app" "asvw" {
  #checkov:skip=CKV_AZURE_63: http logging not scope
  #checkov:skip=CKV_AZURE_80: using dotnet is optional, custom validation implemented for version check
  name                          = local.windows_web_app_name
  resource_group_name           = var.resource_group_name
  location                      = var.conventions.region
  service_plan_id               = var.app_service_plan_id != null ? var.app_service_plan_id : module.aasp[0].aasp.id
  https_only                    = var.https_only
  client_certificate_enabled    = var.client_certificate_enabled == null ? ( var.site_config.http2_enabled ? false : true ) : var.client_certificate_enabled
  client_certificate_mode       = var.client_certificate_mode
  tags                          = local.tags
  virtual_network_subnet_id     = var.delegated_subnet_id
  public_network_access_enabled = false
  

  dynamic "storage_account" {
    for_each = var.storage_accounts
    content {
      account_name  = storage_account.value.stac_object.name
      access_key    = storage_account.value.stac_object.primary_access_key
      name          = storage_account.value.stac_object.name
      share_name    = storage_account.value.share_name
      type          = storage_account.value.type == null ? "AzureFiles" : storage_account.value.type
      mount_path    = storage_account.value.mount_path
    }
  }

  logs {
    detailed_error_messages = var.detailed_error_messages
    failed_request_tracing  = var.failed_request_tracing
  }

  app_settings = merge(var.app_settings, var.dflt_app_settings)

  site_config {
      vnet_route_all_enabled   = var.site_config.vnet_route_all_enabled
      always_on                = var.site_config.always_on
      minimum_tls_version      = var.site_config.minimum_tls_version
      http2_enabled            = var.site_config.http2_enabled
      health_check_eviction_time_in_min = var.site_config.health_check_eviction_time_in_min
      health_check_path        = var.site_config.health_check_path
      ftps_state               = "Disabled"
      remote_debugging_enabled = var.site_config.remote_debugging_enabled

      container_registry_managed_identity_client_id = var.site_config.container_registry_managed_identity_client_id
      container_registry_use_managed_identity       = var.site_config.container_registry_use_managed_identity      

      dynamic "application_stack" {
        for_each = var.site_config.application_stack == null ? [] : [1]
        content {
          current_stack                = var.site_config.application_stack.current_stack
          dotnet_version               = var.site_config.application_stack.dotnet_version
          dotnet_core_version          = var.site_config.application_stack.dotnet_core_version
          tomcat_version               = var.site_config.application_stack.tomcat_version
          java_embedded_server_enabled = var.site_config.application_stack.java_embedded_server_enabled
          java_version                 = var.site_config.application_stack.java_version
          node_version                 = var.site_config.application_stack.node_version
          php_version                  = var.site_config.application_stack.php_version

          docker_image_name            = var.site_config.application_stack.docker_image_name
          docker_registry_url          = var.site_config.application_stack.docker_registry_url
          docker_registry_username     = var.site_config.application_stack.docker_registry_username
          docker_registry_password     = var.site_config.application_stack.docker_registry_password          
        }
      }
  }

  dynamic "auth_settings_v2" {
    for_each = var.auth_settings_v2 == null || var.auth_version != "v2" ? [] : [1]
    content {
      auth_enabled = var.auth_settings_v2.auth_enabled
      login {
        allowed_external_redirect_urls = var.auth_settings_v2.login.allowed_external_redirect_urls
        token_store_enabled            = var.auth_settings_v2.login.token_store_enabled
      }
      default_provider       = "azureactivedirectory"
      http_route_api_prefix  = var.auth_settings_v2.http_route_api_prefix
      require_authentication = true
      require_https          = true
      runtime_version        = "~1"
      unauthenticated_action = var.auth_settings_v2.unauthenticated_action
      dynamic "active_directory_v2" {
        for_each = var.auth_settings_v2.active_directory_v2 == null ? [] : [1]
        content {
          allowed_applications                 = var.auth_settings_v2.active_directory_v2.allowed_applications
          allowed_audiences                    = var.auth_settings_v2.active_directory_v2.allowed_audiences
          allowed_groups                       = var.auth_settings_v2.active_directory_v2.allowed_groups
          allowed_identities                   = var.auth_settings_v2.active_directory_v2.allowed_identities
          client_id                            = var.auth_settings_v2.active_directory_v2.client_id
          client_secret_setting_name           = var.auth_settings_v2.active_directory_v2.client_secret_setting_name
          client_secret_certificate_thumbprint = var.auth_settings_v2.active_directory_v2.client_secret_certificate_thumbprint
          jwt_allowed_client_applications      = var.auth_settings_v2.active_directory_v2.jwt_allowed_client_applications
          jwt_allowed_groups                   = var.auth_settings_v2.active_directory_v2.jwt_allowed_groups
          login_parameters                     = var.auth_settings_v2.active_directory_v2.login_parameters
          tenant_auth_endpoint                 = "https://login.microsoftonline.com/${data.azurerm_client_config.current.tenant_id}/v2.0/"
          www_authentication_disabled          = false
        }
      }
    }
  }  

  // Auth Settings V1 - this is the deafult for backwards compatibility
  dynamic "auth_settings" {
    for_each = var.auth_version != "v1" ? [] : [1]
    content {
      enabled = var.auth_settings.enabled
      active_directory {
        client_id = var.auth_settings.active_directory.client_id
      }
      unauthenticated_client_action = var.auth_settings.unauthenticated_client_action
    }
  }  

  dynamic "connection_string" {
    for_each = var.connection_strings == null ? [] : var.connection_strings
    content {
      name  = connection_string.value.name
      type  = connection_string.value.type
      value = connection_string.value.value
    }
  }  

  // Identity
  identity {
    type         = var.identity_ids != null && var.identity_ids != [] ? "SystemAssigned, UserAssigned" : "SystemAssigned"
    identity_ids = var.identity_ids
  }  
}

module "privateendpoint01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-private-endpoint?ref=v1.3.0"
  conventions          = var.conventions
  resource_group_name  = var.resource_group_name
  location             = var.conventions.region
  resource_name_suffix = "${var.resource_name_suffix}-${local.resource_shortname_asvw}01"
  subnet_id            = var.subnet_id
  resource_id          = azurerm_windows_web_app.asvw.id
  subresource_list     = ["sites"]
  wait_after           = var.pe_wait_after
}

resource "azapi_update_resource" "asvw_update" {
  type        = "Microsoft.Web/sites@2023-12-01"
  resource_id = azurerm_windows_web_app.asvw.id
  body = {
    properties = {       
      vnetImagePullEnabled = var.vnet_image_pull_enabled
      vnetContentShareEnabled = var.vnet_content_share_enabled
      vnetBackupRestoreEnabled = var.vnet_backup_restore_enabled     
    }
  }
}