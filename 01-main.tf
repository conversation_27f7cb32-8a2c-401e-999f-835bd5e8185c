resource "azurerm_ai_services" "aifoundry" {
  name                               = local.aisvcname
  location                           = var.conventions.region
  resource_group_name                = var.resource_group_name
  sku_name                           = var.aifoundry_sku
  local_authentication_enabled       = false
  outbound_network_access_restricted = var.outbound_network_access_restricted
  public_network_access              = var.public_network_access_enabled ? "Enabled" : "Disabled"

  custom_subdomain_name = var.aifoundry_custom_domain_name
  identity {
    type         = "SystemAssigned, UserAssigned"
    identity_ids = var.identity_ids
  }

  customer_managed_key {
    key_vault_key_id   = can(regex("managedhsm", var.customer_managed_key.key_vault_key_id)) ? null : var.customer_managed_key.key_vault_key_id
    managed_hsm_key_id = can(regex("managedhsm", var.customer_managed_key.key_vault_key_id)) ? var.customer_managed_key.key_vault_key_id : null
    identity_client_id = var.customer_managed_key.identity_client_id
  }
  network_acls {
    bypass         = var.network_acl_bypass
    default_action = "Deny"
    ip_rules       = var.conventions.firewall_whitelist
    dynamic "virtual_network_rules" {
      for_each = var.virtual_network_rules
      content {
        subnet_id                            = virtual_network_rules.value.subnet_id
        ignore_missing_vnet_service_endpoint = virtual_network_rules.value.ignore_missing_vnet_service_endpoint
      }
    }
  }
}


// Private endpoint for aifoundry
module "privateendpoint01_aifoundry" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-private-endpoint?ref=v1.3.0"
  conventions          = var.conventions
  resource_group_name  = var.resource_group_name
  location             = var.conventions.region
  resource_name_suffix = "${var.resource_name_suffix}-${local.svc_shortname}"
  subnet_id            = var.subnet_id
  resource_id          = resource.azurerm_ai_services.aifoundry.id
  subresource_list     = ["account"]
}
