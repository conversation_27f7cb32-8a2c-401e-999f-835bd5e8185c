data "azurerm_key_vault_secret" "adminsecret" {
  name         = var.admin_secret_name
  key_vault_id = var.key_vault_id
}

resource "azurerm_windows_virtual_machine" "vm" {
  #checkov:skip=CKV_AZURE_50:We are using Network watcher extension intentionally
  name                       = coalesce(var.vmname_override, local.vm_name)
  computer_name              = coalesce(var.computer_name_override, local.base_name)
  resource_group_name        = var.resource_group_name
  location                   = var.conventions.region
  size                       = var.size
  admin_username             = var.admin_username
  admin_password             = data.azurerm_key_vault_secret.adminsecret.value
  network_interface_ids      = length(var.vnic_ids) > 0 ? var.vnic_ids : [module.vnic[0].vnic.id]
  zone                       = length(var.zone) > 0 ? var.zone : null
  source_image_id            = var.source_image_id
  license_type               = var.license_type
  encryption_at_host_enabled = true
  secure_boot_enabled        = false #Recovery Services Vault

  lifecycle {
    ignore_changes = [
      admin_password
    ]
  }

  boot_diagnostics {
    storage_account_uri = var.boot_diagnostics_storage_account_uri
  }

  dynamic "source_image_reference" {
    for_each = var.source_image_reference == null ? [] : [1]
    content {
      publisher = var.source_image_reference.publisher
      offer     = var.source_image_reference.offer
      sku       = var.source_image_reference.sku
      version   = var.source_image_reference.version
    }
  }

  os_disk {
    name                   = var.os_disk_name_override == null ? local.os_disk_name : var.os_disk_name_override
    caching                = var.os_disk_caching
    storage_account_type   = var.storage_account_type
    disk_encryption_set_id = length(var.disk_encryption_set_id) > 0 ? var.disk_encryption_set_id : null
  }

  additional_capabilities {
    ultra_ssd_enabled = var.ultra_ssd_enabled
  }

  identity {
    type         = var.identity_type
    identity_ids = var.identity_ids
  }

  tags = local.tags
}

data "azurerm_managed_disk" "osdisk" {
  name                = azurerm_windows_virtual_machine.vm.os_disk[0].name
  resource_group_name = lower(var.resource_group_name)
}

// Update OS disk network access
resource "azapi_update_resource" "osdisk" {
  type        = "Microsoft.Compute/disks@2023-01-02"
  resource_id = data.azurerm_managed_disk.osdisk.id
  body = {
    properties = {
      networkAccessPolicy = "DenyAll"
      publicNetworkAccess = "Disabled"
    }
  }
}

moved {
  from = module.vnic
  to   = module.vnic[0]
}

module "vnic" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-vnic?ref=v1.4.0"
  count                = length(var.vnic_ids) > 0 ? 0 : 1
  conventions          = var.conventions
  location             = var.conventions.region
  resource_name_suffix = lower(var.resource_name_suffix)
  subnet_id            = var.subnet_id_for_nics
  resource_group_name  = var.resource_group_name
}

resource "azurerm_virtual_machine_data_disk_attachment" "attached_managed_disk" {
  for_each = { for obj in var.attached_disk_vars : obj.data_disk_logical_unit_number => obj }

  managed_disk_id    = each.value.attachable_managed_disk_id
  virtual_machine_id = azurerm_windows_virtual_machine.vm.id
  lun                = each.value.data_disk_logical_unit_number
  caching            = each.value.attached_data_disk_caching
}

//Below resource is used when VM need to be used as an App Gateway backend
resource "azurerm_network_interface_application_gateway_backend_address_pool_association" "appgw_assoc" {
  count = var.appgw_backend_address_pool_id != null && length(var.vnic_ids) == 0 ? 1 : 0

  network_interface_id    = module.vnic[0].vnic.id
  ip_configuration_name   = var.appgw_vnic_ip_configuration_name
  backend_address_pool_id = var.appgw_backend_address_pool_id
}

resource "azurerm_network_interface_application_gateway_backend_address_pool_association" "appgw_assoc_ext" {
  count = var.appgw_backend_address_pool_id != null ? length(var.vnic_ids) : 0

  network_interface_id    = var.vnic_ids[count.index]
  ip_configuration_name   = var.appgw_vnic_ip_configuration_name
  backend_address_pool_id = var.appgw_backend_address_pool_id
}