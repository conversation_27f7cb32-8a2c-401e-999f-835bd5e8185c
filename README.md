## Name of the module
Azure Windows VM Module

shortname: wnvm

terraform resource: azurerm_windows_virtual_machine

## Short description of the module
This Terraform module deploys Azure Windows VM.

## Detailed description on Confluence
[Azure Windows VM](https://confluence.otpbank.hu/x/0F8oKQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.0.1
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- Azure Windows VM
