# Name of the module 
Azure Windows App Service Module (Web App, Microsoft.Web/sites)

Shortname: asvw

Terraform resource: azurerm_windows_web_app

# Short description of the module 
This Terraform module deploys windows based Web App Service in Azure with a Private Endpoint.

# Link to detailed description on Confluence
[Azure Windows App Service](https://confluence.otpbank.hu/x/UQdOLg)

## Terraform version compatibility
Terraform >= v1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= v4.0.1
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- App Service Plan (windows version, note os_type = "windows" is hard coded)
- Web App
- Private EndPoint for the Web App


