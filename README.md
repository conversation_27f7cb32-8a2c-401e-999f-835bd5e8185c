## Name of the module
Azure AI Foundry Module

shortname: aihu

terraform resources created by this module: 
- azapi_resource/Microsoft.MachineLearningServices/workspaces
- azapi_resource/Microsoft.CognitiveServices/accounts
- azapi_resource/Microsoft.MachineLearningServices/workspaces/connections

## Short description of the module
This Terraform module deploys Azure AI Foundry HUB and project.

## Detailed description on Confluence
[Azure AI Foundry](https://confluence.otpbank.hu/x/uxAPQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.110.0
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md) 

## Resources generated by the module
- Azure AI Foundry HUB
- Azure AI Project
- Private endpoints
