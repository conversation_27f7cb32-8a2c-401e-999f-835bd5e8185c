//Diagnostic settings
module "asvw_logging" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  count                          = length(var.log_analytics_metrics) != 0 || length(var.log_analytics_diag_logs) != 0 ? 1 : 0
  source                         = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-diag?ref=v1.2.0"
  resource_name_suffix           = var.resource_name_suffix
  conventions                    = var.conventions
  diag_target_resource_id        = azurerm_windows_web_app.asvw.id
  diag_loganalytics_workspace_id = var.log_analytics_workspace_id
  diag_loganalytics_diag_logs    = var.log_analytics_diag_logs
  diag_loganalytics_metrics      = var.log_analytics_metrics
}

//Resource health alert
module "resource_health_asvw" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5.0"
  resource_health_alert = true
  conventions           = var.conventions
  resource_group_name   = var.resource_group_name
  alert_location        = var.resource_health_alert_location
  resource_name_suffix  = "asvw-resourcehealth-${var.resource_name_suffix}"
  scopes                = [azurerm_windows_web_app.asvw.id]
}
