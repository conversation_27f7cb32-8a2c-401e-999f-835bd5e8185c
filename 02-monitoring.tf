//AZ Network Watcher Installation
resource "azurerm_virtual_machine_extension" "az_network_watcher" {
  count                      = var.az_network_watcher_installation == true ? 1 : 0
  name                       = "AzureNetworkWatcherExtension"
  virtual_machine_id         = azurerm_windows_virtual_machine.vm.id
  publisher                  = "Microsoft.Azure.NetworkWatcher"
  type_handler_version       = "1.4"
  type                       = "NetworkWatcherAgentWindows"
  auto_upgrade_minor_version = var.az_network_watcher_auto_minor_upgrage
  automatic_upgrade_enabled  = var.az_network_watcher_auto_upgrade
  depends_on                 = [azurerm_windows_virtual_machine.vm]
}

//AMA Installation
resource "azurerm_virtual_machine_extension" "ama_windows" {
  count                      = var.ama_installation == true ? 1 : 0
  name                       = "AzureMonitorWindowsAgent"
  virtual_machine_id         = azurerm_windows_virtual_machine.vm.id
  publisher                  = "Microsoft.Azure.Monitor"
  type                       = "AzureMonitorWindowsAgent"
  type_handler_version       = "1.19"
  auto_upgrade_minor_version = var.ama_auto_minor_upgrade
  automatic_upgrade_enabled  = var.ama_auto_upgrade
  depends_on                 = [azurerm_windows_virtual_machine.vm]
}

//DCE association
resource "azurerm_monitor_data_collection_rule_association" "dce_association" {
  count                       = var.dce_association == true ? 1 : 0
  name                        = "configurationAccessEndpoint"
  target_resource_id          = azurerm_windows_virtual_machine.vm.id
  data_collection_endpoint_id = var.conventions.dce_win_id
  description                 = "Association between ${azurerm_windows_virtual_machine.vm.name} as VM and DCE"
  depends_on                  = [azurerm_virtual_machine_extension.ama_windows]
}

//Data collection rule
resource "azurerm_monitor_data_collection_rule" "dcrl" {
  count               = var.eventlog_settings != null || var.perf_counters != null ? 1 : 0
  name                = "dcrl-${local.resource_name}"
  resource_group_name = azurerm_windows_virtual_machine.vm.resource_group_name
  location            = azurerm_windows_virtual_machine.vm.location
  kind                = "Windows"

  data_collection_endpoint_id = var.conventions.dce_win_id

  destinations {
    log_analytics {
      workspace_resource_id = var.log_analytics_workspace_id == null ? var.conventions.log_analytics_workspace_id : var.log_analytics_workspace_id
      name                  = "win-vm-destination-log"
    }
  }

  data_flow {
    streams      = ["Microsoft-Perf"]
    destinations = ["win-vm-destination-log"]
  }

  data_flow {
    streams      = ["Microsoft-Event"]
    destinations = ["win-vm-destination-log"]
  }

  data_sources {
    dynamic "windows_event_log" {
      for_each = var.eventlog_settings != null ? var.eventlog_settings : []
      content {
        name           = windows_event_log.value.name
        streams        = windows_event_log.value.streams
        x_path_queries = windows_event_log.value.x_path_queries
      }
    }


    dynamic "performance_counter" {
      for_each = var.perf_counters != null ? var.perf_counters : []
      content {
        counter_specifiers            = performance_counter.value.counter_specifiers
        name                          = performance_counter.value.name
        sampling_frequency_in_seconds = performance_counter.value.sampling_frequency_in_seconds
        streams                       = ["Microsoft-Perf"]
      }
    }
  }

}

resource "azurerm_monitor_data_collection_rule_association" "dcrl_association" {
  count                   = var.eventlog_settings != null ? 1 : 0
  name                    = "dcrl-${azurerm_windows_virtual_machine.vm.name}"
  target_resource_id      = azurerm_windows_virtual_machine.vm.id
  data_collection_rule_id = azurerm_monitor_data_collection_rule.dcrl[0].id
  description             = "Association between ${azurerm_windows_virtual_machine.vm.name} as VM and DCR ${azurerm_monitor_data_collection_rule.dcrl[0].name}"
}


