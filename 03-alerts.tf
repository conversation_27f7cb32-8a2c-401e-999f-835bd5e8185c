//----------------------------------------------------------------- Resource health alert
module "resource_health_vm" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5.0"
  resource_health_alert = true
  conventions           = var.conventions
  resource_group_name   = var.resource_group_name
  alert_location        = var.resource_health_alert_location
  resource_name_suffix  = lower("wnvm-resourcehealth-${var.resource_name_suffix}")

  scopes = [azurerm_windows_virtual_machine.vm.id]
}

// --------------------------------------------------------------- Default metric alerts
module "builtin_metrics_win_vm" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count               = var.builtin_metric_monitoring ? 1 : 0
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//metric_alert?ref=v1.5.0"
  conventions         = var.conventions
  resource_group_name = var.resource_group_name


  metric_alerts = {
    "Data Disk Bandwidth Consumed Percentage" = {
      resource_name_suffix = lower("wnvm-DataDiskBandwidthConsumedPercentage-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Percentage of data disk bandwidth consumed per minute. Only available on VM series that support premium storage."
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "Data Disk Bandwidth Consumed Percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_Data_Disk_Bandwidth_Consumed_Percentage_threshold //default 90
        }
      ]
    },
    "Data Disk IOPS Consumed Percentage" = {
      resource_name_suffix = lower("wnvm-DataDiskIOPSConsumedPercentage-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Percentage of data disk I/Os consumed per minute. Only available on VM series that support premium storage."
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "Data Disk IOPS Consumed Percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_Data_Disk_IOPS_Consumed_Percentage_threshold //default 90
        }
      ]
    },
    "OS Disk Bandwidth Consumed Percentage" = {
      resource_name_suffix = lower("wnvm-OSDiskBandwidthConsumedPercentage-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Percentage of operating system disk bandwidth consumed per minute. Only available on VM series that support premium storage."
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "OS Disk Bandwidth Consumed Percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_OS_Disk_Bandwidth_Consumed_Percentage_threshold //default 90
        }
      ]
    },
    "OS Disk IOPS Consumed Percentage" = {
      resource_name_suffix = lower("wnvm-OSDiskIOPSConsumedPercentage-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Percentage of data disk bandwidth consumed per minute. Only available on VM series that support premium storage."
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "OS Disk IOPS Consumed Percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_Data_Disk_Bandwidth_Consumed_Percentage_threshold //default 90
        }
      ]
    },
    "Percentage CPU" = {
      resource_name_suffix = lower("wnvm-PercentageCPU-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Percentage CPU The percentage of allocated compute units that are currently in use by the Virtual Machine(s)"
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "Percentage CPU"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_percentage_CPU_threshold //default 90
        }
      ]
    },
    "Available Memory Bytes" = {
      resource_name_suffix = lower("wnvm-AvailableMemoryBytes-${var.resource_name_suffix}")
      scopes               = [azurerm_windows_virtual_machine.vm.id]
      description          = "Amount of physical memory, in bytes, immediately available for allocation to a process or for system use in the Virtual Machine"
      severity             = 2
      frequency            = "PT5M"
      window_size          = "PT5M"

      criteria = [
        {
          metric_namespace = "Microsoft.Compute/virtualMachines"
          metric_name      = "Available Memory Bytes"
          aggregation      = "Average"
          operator         = "LessThan"
          threshold        = var.alert_Available_Memory_Bytes_threshold //default 1000
        }
      ]
    }
  }
}

