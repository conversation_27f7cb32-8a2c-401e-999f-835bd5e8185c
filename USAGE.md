<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Windows VM Module

shortname: wnvm

terraform resource: azurerm\_windows\_virtual\_machine

## Short description of the module
This Terraform module deploys Azure Windows VM.

## Detailed description on Confluence
[Azure Windows VM](https://confluence.otpbank.hu/x/0F8oKQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.0.1
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- Azure Windows VM

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azapi"></a> [azapi](#requirement\_azapi) (>= 2.1.0)

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.0.1)

## Providers

The following providers are used by this module:

- <a name="provider_azapi"></a> [azapi](#provider\_azapi) (>= 2.1.0)

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.0.1)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {}
  alias           = "image_source"
  subscription_id = module.conventions.azure_images.windows.subscription_id #the subscription id of the default image gallery
}

#provider - All provider configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "azapi" {
  use_oidc = true
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    azapi = {
      source  = "Azure/azapi"
      version = "2.1.0"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.1"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for Windows Virtual Machine creation

```hcl

locals {
  project       = "wvm"
  index         = "19"
  suffix        = "${local.project}-${local.index}"
  resource_name = "${module.conventions.short_region}-${module.conventions.environment}-${local.suffix}"
}

// Generate admin password and add it to key vault
module "password" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//password?ref=v2.1.1"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "secret" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-secret?ref=v1.5.0"
  conventions     = module.conventions
  key_vault_id    = data.azurerm_key_vault.kv.id
  name            = local.resource_name
  value           = module.password.password
  expiration_date = time_offset.expirationdate.rfc3339
}

module "rg" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "managed_disk_module_1" {
  //OPTIONAL MODULE! If you don't need it you can skip the managed_disk_module1 2 ...
  //Checkov
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk//manageddisk?ref=v1.3.0"
  conventions           = module.conventions
  resource_group_name   = module.rg.rgrp.name
  resource_name_suffix  = "${local.project}-01"
  disk_size_gb          = "16"
  network_access_policy = "DenyAll"
}

module "managed_disk_module_2" {
  //OPTIONAL MODULE! If you don't need it you can skip the managed_disk_module1 2 ...
  //Checkov
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk//manageddisk?ref=v1.3.0"
  conventions           = module.conventions
  resource_group_name   = module.rg.rgrp.name
  resource_name_suffix  = "${local.project}-02"
  disk_size_gb          = "16"
  network_access_policy = "DenyAll"
}

module "vmtest" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-vm?ref=v3.1.1"
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  resource_group_name  = module.rg.rgrp.name
  function             = "app"
  index                = local.index
  size                 = "Standard_D2s_v4"
  subnet_id_for_nics   = data.azurerm_subnet.vm_subnet.id //Using an existing subnet
  admin_username       = "azureadmin"
  key_vault_id         = data.azurerm_key_vault.kv.id //Using an existing key vault where the secret is stored
  admin_secret_name    = module.secret.azurerm_key_vault_secret.name 
  zone                 = ""                                                //Keep empty string ("") if you don't want to specify the Availabilty Zone, otherwise please enter the zone ("1" || "2" || "3" || etc)
  source_image_id      = data.azurerm_shared_image.shared_image.id #referring to gallery image id, which is defined in data block
  license_type         = "Windows_Server" //Make sure this suits the image you are using. Possible values are None, Windows_Client and Windows_Server.

  az_network_watcher_installation = false

  //In case you need to swap the OS disk outside of Terraform, use this parameter to refresh Terraform configuration:
  //os_disk_name_override = "odsk-weu-tst-wvm-191"

  //builtin_metric_monitoring  = false
  //resource_health_monitoring = false
  
  // Optional block to configure eventlog collection to log analytics workspace
  // If log_analytics_workspace_id is not defined data is sent to shared log analytics workspace

  eventlog_settings = [
    {
      name = "dcr-data-source-eventlogs"
      streams = ["Microsoft-Event"]
      x_path_queries = ["Application!*[System[(Level=1 or Level=2 or Level=3)]]","System!*[System[(Level=1 or Level=2 or Level=3)]]"]
    }
  ]

  // Optional parameter to send additional performance metrics to log analytics workspace
  perf_counters = [
    {
      name                          = "perfmetrics"
      counter_specifiers            = ["\\Memory\\Available Bytes","\\System\\Processes"]
      sampling_frequency_in_seconds = 120
    }
  ]

  //Optional block to create auto-shutdown schedule
  /*
  auto_shutdown = {
    daily_recurrence_time = "1900"
  }
  */
 
  //Following code block can be omitted
  //Start of omittable block
  //It is not mandatory to add these variables into your blueprint, the following lines describes the situation when you want to attach one or more attached disks to the VM
   attached_disk_vars = [{
    attachable_managed_disk_id    = module.managed_disk_module_1.mdsk.id,
    data_disk_logical_unit_number = 0,     //Must be unique value among the disks!
    attached_data_disk_caching    = "None" // Possible values are None, ReadOnly and ReadWrite
    },
    {
      attachable_managed_disk_id    = module.managed_disk_module_2.mdsk.id,
      data_disk_logical_unit_number = 1,          //Must be unique value among the disks!
      attached_data_disk_caching    = "ReadWrite" // Possible values are None, ReadOnly and ReadWrite
  }]
  //End of omittable block

  depends_on = [ module.secret ]
}


```



## Resources

The following resources are used by this module:

- [azapi_update_resource.osdisk](https://registry.terraform.io/providers/Azure/azapi/latest/docs/resources/update_resource) (resource)
- [azurerm_dev_test_global_vm_shutdown_schedule.auto_shutdown](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/dev_test_global_vm_shutdown_schedule) (resource)
- [azurerm_monitor_data_collection_rule.dcrl](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule) (resource)
- [azurerm_monitor_data_collection_rule_association.dce_association](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule_association) (resource)
- [azurerm_monitor_data_collection_rule_association.dcrl_association](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule_association) (resource)
- [azurerm_network_interface_application_gateway_backend_address_pool_association.appgw_assoc](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_interface_application_gateway_backend_address_pool_association) (resource)
- [azurerm_network_interface_application_gateway_backend_address_pool_association.appgw_assoc_ext](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/network_interface_application_gateway_backend_address_pool_association) (resource)
- [azurerm_virtual_machine_data_disk_attachment.attached_managed_disk](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/virtual_machine_data_disk_attachment) (resource)
- [azurerm_virtual_machine_extension.ama_windows](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/virtual_machine_extension) (resource)
- [azurerm_virtual_machine_extension.az_network_watcher](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/virtual_machine_extension) (resource)
- [azurerm_windows_virtual_machine.vm](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/windows_virtual_machine) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_admin_secret_name"></a> [admin\_secret\_name](#input\_admin\_secret\_name)

Description: (Required) Name of the Key Vault Secret, where the administrator password is stored.

Type: `string`

### <a name="input_admin_username"></a> [admin\_username](#input\_admin\_username)

Description: (Required) The username of the local administrator used for the Virtual Machine. Changing this forces a new resource to be created.

Type: `string`

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) Custom parameter modul

Type: `any`

### <a name="input_function"></a> [function](#input\_function)

Description: (Required) Part of the custom naming

Type: `string`

### <a name="input_index"></a> [index](#input\_index)

Description: (Required) Part of the custom naming

Type: `string`

### <a name="input_key_vault_id"></a> [key\_vault\_id](#input\_key\_vault\_id)

Description: (Required) The ID of the Key Vault where the admin password is stored.

Type: `string`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) The name of the Resource Group in which the Windows Virtual Machine should be exist. Changing this forces a new resource to be created.

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_size"></a> [size](#input\_size)

Description:  (Required) The SKU which should be used for this Virtual Machine, such as Standard\_F2.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_alert_Available_Memory_Bytes_threshold"></a> [alert\_Available\_Memory\_Bytes\_threshold](#input\_alert\_Available\_Memory\_Bytes\_threshold)

Description: Threshold for Available Memory Bytes alert rule

Type: `number`

Default: `1000`

### <a name="input_alert_Data_Disk_Bandwidth_Consumed_Percentage_threshold"></a> [alert\_Data\_Disk\_Bandwidth\_Consumed\_Percentage\_threshold](#input\_alert\_Data\_Disk\_Bandwidth\_Consumed\_Percentage\_threshold)

Description: Threshold for Data Disk Bandwidth Consumed Percentage alert rule.

Type: `number`

Default: `90`

### <a name="input_alert_Data_Disk_IOPS_Consumed_Percentage_threshold"></a> [alert\_Data\_Disk\_IOPS\_Consumed\_Percentage\_threshold](#input\_alert\_Data\_Disk\_IOPS\_Consumed\_Percentage\_threshold)

Description: Threshold for Data Disk IOPS Consumed Percentage alert rule.

Type: `number`

Default: `90`

### <a name="input_alert_OS_Disk_Bandwidth_Consumed_Percentage_threshold"></a> [alert\_OS\_Disk\_Bandwidth\_Consumed\_Percentage\_threshold](#input\_alert\_OS\_Disk\_Bandwidth\_Consumed\_Percentage\_threshold)

Description: Threshold for OS Disk Bandwidth Consumed Percentage alert rule.

Type: `number`

Default: `90`

### <a name="input_alert_OS_Disk_IOPS_Consumed_Percentage_threshold"></a> [alert\_OS\_Disk\_IOPS\_Consumed\_Percentage\_threshold](#input\_alert\_OS\_Disk\_IOPS\_Consumed\_Percentage\_threshold)

Description: Threshold for OS Disk IOPS Consumed Percentage alert rule

Type: `number`

Default: `90`

### <a name="input_alert_percentage_CPU_threshold"></a> [alert\_percentage\_CPU\_threshold](#input\_alert\_percentage\_CPU\_threshold)

Description: Threshold for Percentage CPU alert rule.

Type: `number`

Default: `90`

### <a name="input_ama_auto_minor_upgrade"></a> [ama\_auto\_minor\_upgrade](#input\_ama\_auto\_minor\_upgrade)

Description: (Optional) Specifies if the platform deploys the latest minor version update to the type\_handler\_version specified. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_ama_auto_upgrade"></a> [ama\_auto\_upgrade](#input\_ama\_auto\_upgrade)

Description: (Optional) Should the Extension be automatically updated whenever the Publisher releases a new version of Azure Monitor Agent. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_ama_installation"></a> [ama\_installation](#input\_ama\_installation)

Description: (Optional) Azure Monitor Agent must be running on all VMs. Set this parameter to false only if you want to let Azure policies to install the agent.

Type: `bool`

Default: `true`

### <a name="input_appgw_backend_address_pool_id"></a> [appgw\_backend\_address\_pool\_id](#input\_appgw\_backend\_address\_pool\_id)

Description: (Optional) Use this variable only if you want to use the VM as an Application Gateway backend. The ID of the Application Gateway's Backend Address Pool which this Network Interface which should be connected to. Changing this forces a new resource to be created.

Type: `string`

Default: `null`

### <a name="input_appgw_vnic_ip_configuration_name"></a> [appgw\_vnic\_ip\_configuration\_name](#input\_appgw\_vnic\_ip\_configuration\_name)

Description: (Optional) The Name of the IP Configuration within the Network Interface which should be connected to the Backend Address Pool. Changing this forces a new resource to be created. Defaults to internal which is set by vnic module.

Type: `string`

Default: `"internal"`

### <a name="input_attached_disk_vars"></a> [attached\_disk\_vars](#input\_attached\_disk\_vars)

Description: The attached\_disk\_vars variable has 3 parameters. Default value is []. If you want to attach one or more disks you must add value to all 3 variables:  
 attachable\_managed\_disk\_id - (Required) The ID of the attached disk which you want to attach. The variable attached\_disk\_vars must be set as in example 01, then the module creates as many managed disks as many necessary (including 0) and attaches it to the current VM  
 data\_disk\_logical\_unit\_number - (Required) The Logical Unit Number of the Data Disk, which MUST be unique within the Virtual Machine. Changing this forces a new resource to be created. Default value is 0  
 attached\_data\_disk\_caching - (Required) Specifies the caching requirements for this Data Disk. Possible values include None, ReadOnly and ReadWrite. Default value is None

Type:

```hcl
list(object({
    attachable_managed_disk_id    = optional(string)
    data_disk_logical_unit_number = optional(number)
    attached_data_disk_caching    = optional(string)
  }))
```

Default: `[]`

### <a name="input_auto_shutdown"></a> [auto\_shutdown](#input\_auto\_shutdown)

Description:     (Optional) Manages an automated shutdown schedule for the VM. If this variable is not used auto-shutdown schedule is NOT configured.  
      daily\_recurrence\_time     - (Required) The time each day when the schedule takes effect. Must match the format HHmm where HH is 00-23 and mm is 00-59 (e.g. 0930, 2300, etc.)  
      enabled                   - (Optional) Whether to enable the schedule. Possible values are true and false. Defaults to true.  
      timezone                  - (Optional) The time zone ID (e.g. Pacific Standard time). Refer to this guide for a full list of accepted time zone names: https://jackstromberg.com/2017/01/list-of-time-zones-consumed-by-azure/. Defaults to Central Europe Standard Time.  
      notification\_enabled      - (Optional) Whether to enable pre-shutdown notifications. Possible values are true and false. Defaults to false  
      notification\_time\_in\_mins - (Optional) Time in minutes between 15 and 120 before a shutdown event at which a notification will be sent. Defaults to 30.  
      notification\_webhook\_url  - (Optional) The webhook URL to which the notification will be sent.  
      notification\_email        - (Optional) (Optional) E-mail address to which the notification will be sent.

Type:

```hcl
object({
    enabled                   = optional(bool,true)
    timezone                  = optional(string,"Central Europe Standard Time")
    daily_recurrence_time     = string
    notification_enabled      = optional(bool,false)
    notification_time_in_mins = optional(number,30)
    notification_webhook_url  = optional(string,null)
    notification_email        = optional(string,null)
  })
```

Default: `null`

### <a name="input_az_network_watcher_auto_minor_upgrage"></a> [az\_network\_watcher\_auto\_minor\_upgrage](#input\_az\_network\_watcher\_auto\_minor\_upgrage)

Description: (Optional) Specifies if the platform deploys the latest minor version update to the type\_handler\_version specified. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_az_network_watcher_auto_upgrade"></a> [az\_network\_watcher\_auto\_upgrade](#input\_az\_network\_watcher\_auto\_upgrade)

Description: (Optional) Should the Extension be automatically updated whenever the Publisher releases a new version of AZ Network Watcher. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_az_network_watcher_installation"></a> [az\_network\_watcher\_installation](#input\_az\_network\_watcher\_installation)

Description: (Optional) AZ Network Watcher can be installed on VM. defaults to false.

Type: `bool`

Default: `false`

### <a name="input_boot_diagnostics_storage_account_uri"></a> [boot\_diagnostics\_storage\_account\_uri](#input\_boot\_diagnostics\_storage\_account\_uri)

Description: (Optional) The Primary/Secondary Endpoint for the Azure Storage Account which should be used to store Boot Diagnostics, including Console Output and Screenshots from the Hypervisor. Passing a null value will utilize a Managed Storage Account to store Boot Diagnostics.

Type: `string`

Default: `null`

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_compute_gallery_image"></a> [compute\_gallery\_image](#input\_compute\_gallery\_image)

Description: (Optional A compute\_gallery\_image block as defined in examples.)

Type:

```hcl
object({
    version             = string
    name                = string
    gallery_name        = string
    resource_group_name = string
    subscription_id     = string
  })
```

Default: `null`

### <a name="input_computer_name_override"></a> [computer\_name\_override](#input\_computer\_name\_override)

Description: (Optional) Override the operating system computer name

Type: `string`

Default: `null`

### <a name="input_dce_association"></a> [dce\_association](#input\_dce\_association)

Description: (Optional) VMs must be associated with DCEs to send logs through AMPLS. Set this parameter to false only if you want to let Azure policies to perform DCE association.

Type: `bool`

Default: `true`

### <a name="input_disk_encryption_set_id"></a> [disk\_encryption\_set\_id](#input\_disk\_encryption\_set\_id)

Description: (Optional) The ID of the Disk Encryption Set which should be used to Encrypt this OS Disk.

Type: `string`

Default: `""`

### <a name="input_domain_name"></a> [domain\_name](#input\_domain\_name)

Description: (Optional) The name of the AD domain where the VM is goint to join.

Type: `string`

Default: `"corp.otpbank.hu"`

### <a name="input_eventlog_settings"></a> [eventlog\_settings](#input\_eventlog\_settings)

Description: eventlog\_settings variable need to be defined if logging is required.  
Define one or more eventlog logging configuration with following parameters:  
  name - (Required) The name which should be used for this data source. This name should be unique across all data sources regardless of type within the Data Collection Rule.  
  streams - (Required) Specifies a list of streams that this data source will be sent to. A stream indicates what schema will be used for this data and usually what table in Log Analytics the data will be sent to. Possible values include but not limited to Microsoft-Event,and Microsoft-WindowsEvent, Microsoft-RomeDetectionEvent, and Microsoft-SecurityEvent.  
  x\_path\_queries - (Required) Specifies a list of Windows Event Log queries in XPath expression. Please see this document for more information.

Type:

```hcl
list(object({
    name           = string
    streams        = list(string)
    x_path_queries = list(string)
  }))
```

Default: `null`

### <a name="input_guest_config_enabled"></a> [guest\_config\_enabled](#input\_guest\_config\_enabled)

Description: (Optional) This parameter must be set to true if guest configuration policies are applied to the machine. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_identity_ids"></a> [identity\_ids](#input\_identity\_ids)

Description: (Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Windows Virtual Machine.

Type: `list(string)`

Default: `null`

### <a name="input_identity_type"></a> [identity\_type](#input\_identity\_type)

Description: (Optional) Specifies the type of Managed Service Identity that should be configured on this Windows Virtual Machine. Possible values are SystemAssigned (default), UserAssigned, SystemAssigned, UserAssigned (to enable both).

Type: `string`

Default: `"SystemAssigned"`

### <a name="input_license_type"></a> [license\_type](#input\_license\_type)

Description: (Optional) Specifies the type of on-premise license (also known as Azure Hybrid Use Benefit) which should be used for this Virtual Machine. Possible values are None, Windows\_Client and Windows\_Server.

Type: `string`

Default: `"Windows_Server"`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: Optional. If eventlog\_settings is defined and this variable is not defined logs will be sent to shared log analytics workspace. Define this variable if you need different destination workspace.

Type: `string`

Default: `null`

### <a name="input_os_disk_caching"></a> [os\_disk\_caching](#input\_os\_disk\_caching)

Description: (Optional) Specifies the caching requirements for the Data Disk. Possible values include None, ReadOnly and ReadWrite. Defaults to ReadWrite

Type: `string`

Default: `"ReadWrite"`

### <a name="input_os_disk_name_override"></a> [os\_disk\_name\_override](#input\_os\_disk\_name\_override)

Description: (Optional) Override the OS disk name. This parameter need to be used when OS disk was swapped outside of Terraform. azapi\_update\_resource will be still required and executed at the next apply.

Type: `string`

Default: `null`

### <a name="input_perf_counters"></a> [perf\_counters](#input\_perf\_counters)

Description: Collect additional performance counters by using this variable. Available metrics: https://learn.microsoft.com/en-us/azure/azure-monitor/agents/data-sources-performance-counters
(Optional) One or more perf\_counter blocks as defined below.  
  name - (Required) The name which should be used for this data source. This name should be unique across all data sources regardless of type within the Data Collection Rule.  
  counter\_specifiers - (Required) Specifies a list of specifier names of the performance counters you want to collect. To get a list of performance counters on Windows, run the command typeperf. Please see this document for more information.      
  sampling\_frequency\_in\_seconds - (Required) The number of seconds between consecutive counter measurements (samples). The value should be integer between 1 and 300 inclusive. sampling\_frequency\_in\_seconds must be equal to 60 seconds for counters collected with Microsoft-InsightsMetrics stream.

Type:

```hcl
list(object({
    counter_specifiers            = list(string)
    name                          = string
    sampling_frequency_in_seconds = number
  }))
```

Default: `null`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_sccm_group"></a> [sccm\_group](#input\_sccm\_group)

Description: (Optional) The name of SCCM group that will take care of VM patching.

Type: `string`

Default: `"sccm-every-sun-08-18"`

### <a name="input_source_image_id"></a> [source\_image\_id](#input\_source\_image\_id)

Description: (Optional) The ID of the Image which this Virtual Machine should be created from. Changing this forces a new resource to be created. Possible Image ID types include Image IDs, Shared Image IDs, Shared Image Version IDs, Community Gallery Image IDs, Community Gallery Image Version IDs, Shared Gallery Image IDs and Shared Gallery Image Version IDs.

Type: `string`

Default: `null`

### <a name="input_source_image_reference"></a> [source\_image\_reference](#input\_source\_image\_reference)

Description: (Optional) A source\_image\_reference block as defined below. Changing this forces a new resource to be created.

Type:

```hcl
object({
    publisher = string
    offer     = string
    sku       = string
    version   = string
  })
```

Default: `null`

### <a name="input_storage_account_type"></a> [storage\_account\_type](#input\_storage\_account\_type)

Description: (Required) The Type of Storage Account which should back this the Internal OS Disk. Defaults to Premium\_LRS.

Type: `string`

Default: `"Premium_LRS"`

### <a name="input_subnet_id_for_nics"></a> [subnet\_id\_for\_nics](#input\_subnet\_id\_for\_nics)

Description: (Required) the ID of the subnet for the Network Interface (vnic)

Type: `string`

Default: `""`

### <a name="input_ultra_ssd_enabled"></a> [ultra\_ssd\_enabled](#input\_ultra\_ssd\_enabled)

Description: (Optional) Should the capacity to enable Data Disks of the UltraSSD\_LRS storage account type be supported on this Virtual Machine? Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_vmname_override"></a> [vmname\_override](#input\_vmname\_override)

Description: (Optional) Override the Virtual Machine resource name

Type: `string`

Default: `null`

### <a name="input_vnic_ids"></a> [vnic\_ids](#input\_vnic\_ids)

Description: (Optional) List of externally defined VNIC IDs

Type: `list(string)`

Default: `[]`

### <a name="input_winvm_tags"></a> [winvm\_tags](#input\_winvm\_tags)

Description: Tags to applied to Windows VM

Type: `map(string)`

Default: `null`

### <a name="input_zone"></a> [zone](#input\_zone)

Description: (Optional) Specifies the Availability Zone in which this Windows Virtual Machine should be located. Changing this forces a new Windows Virtual Machine to be created. Keep empty string if you don't want to specify the Availabilty Zone, otherwise please enter the zone

Type: `string`

Default: `""`

## Outputs

The following outputs are exported:

### <a name="output_vm"></a> [vm](#output\_vm)

Description: The created azurerm\_windows\_virtual\_machine resource

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->