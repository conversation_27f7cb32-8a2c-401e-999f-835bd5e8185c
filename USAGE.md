<!-- BEGIN_TF_DOCS -->
# Name of the module
Azure Windows App Service Module (Web App, Microsoft.Web/sites)

Shortname: asvw

Terraform resource: azurerm\_windows\_web\_app

# Short description of the module
This Terraform module deploys windows based Web App Service in Azure with a Private Endpoint.

# Link to detailed description on Confluence
[Azure Windows App Service](https://confluence.otpbank.hu/x/UQdOLg)

## Terraform version compatibility
Terraform >= v1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= v4.0.1
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- App Service Plan (windows version, note os\_type = "windows" is hard coded)
- Web App
- Private EndPoint for the Web App

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azapi"></a> [azapi](#requirement\_azapi) (>= 2.1.0)

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.0.1)

## Providers

The following providers are used by this module:

- <a name="provider_azapi"></a> [azapi](#provider\_azapi) (>= 2.1.0)

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.0.1)


## Example for Provider configuration

```hcl
#provider - All provider configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true  
}

provider "azapi" {
  use_oidc = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    azapi = {
      source  = "Azure/azapi"
      version = "2.1.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.9.1"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
//Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.5"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for windows web app creation

```hcl
locals {
  suffix = "winapp01"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}


//Dinamic subnet creation is used for testing, not recommended in production!
module "subnet_tools" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/tooling//snet-tools?ref=v4.3.1"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  subnet_prefixes_length = [28]
}

module "delegated_subnet" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.4.0"
  conventions            = module.conventions
  resource_name_suffix   = "${local.suffix}-dlg"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table = data.azurerm_route_table.default_rtbl.id
  delegation = [{
    name = "managedinstancedelegation"
    service_delegation = {
      name    = "Microsoft.Web/serverFarms"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
    }
  }]
  address_prefix = module.subnet_tools.next_subnets[0]
}

// Optionally one or more storage account can be connected to webapp - azapi provider is required for storageaccount >= v1.2.0 
module "sa" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  #checkov:skip=CKV2_AZURE_21: Logging is executed via security policies
  #checkov:skip=CKV_AZURE_34: Default value is private.
  source                        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount?ref=v3.0.0"
  conventions                   = module.conventions
  resource_group_name           = module.rg01.rgrp.name
  resource_name_suffix          = local.suffix
  subnet_id                     = data.azurerm_subnet.privateendpoint.id
  wait_after                    = 120
  public_network_access_enabled = false
  shared_access_key_enabled     = true    // The Files & Table Storage API's do not support authenticating via AzureAD and will continue to use a SharedKey to access the API's.
  account_tier                  = "Premium"
  account_kind                  = "FileStorage"
  subresource_list              = ["file"]
  account_replication_type      = "ZRS"
  large_file_share_enabled      = true

  //For this test monitoring can be disabled
  resource_health_monitoring = false
  builtin_metric_monitoring  = false

  stac_share = {
    share1 = {
      name                      = "webapp-share01"
      quota_gb                  = 100
      enabled_protocol          = "SMB"
      container_access_type     = "private"
    }
  }

}

module "mywindowswebapp" {
  # source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-web-app?ref=v4.0.1"
  source                        = "../.."
  conventions                   = module.conventions
  resource_group_name           = module.rg01.rgrp.name
  resource_name_suffix          = "01"
  subnet_id                     = data.azurerm_subnet.privateendpoint.id
  delegated_subnet_id           = module.delegated_subnet.snet.id
  azurerm_service_plan_sku_name = "P1v3"
  worker_count                  = 2

  site_config = {
    always_on              = true
    health_check_path      = "/"
    health_check_eviction_time_in_min = 2
    application_stack = {
      current_stack  = "dotnet"
      dotnet_version = "v7.0"
    }
  }

  // Optional storage account connection
  storage_accounts = [
    {
      stac_object  = module.sa.stac
      share_name   = "webapp-share01"
      type         = "AzureFiles"
      mount_path   = "\\mounts\\webapp"
    }
  ]

  app_settings = {
    "WEBSITE_DNS_SERVER" : "**********" //This is the default value, any other value will be overwritten.
    #"WEBSITE_RUN_FROM_PACKAGE" : 1
  }

  auth_settings = {
    enabled = true
    active_directory = {
      client_id = data.azurerm_client_config.current.client_id
    }
  }

  // Log analytics parameters are optional below
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["AppServiceAntivirusScanAuditLogs","AppServiceAppLogs","AppServiceAuditLogs","AppServiceConsoleLogs","AppServiceFileAuditLogs","AppServiceHTTPLogs","AppServiceIPSecAuditLogs","AppServicePlatformLogs","FunctionAppLogs","WorkflowRuntime"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]
}


```



## Resources

The following resources are used by this module:

- [azapi_update_resource.asvw_update](https://registry.terraform.io/providers/Azure/azapi/latest/docs/resources/update_resource) (resource)
- [azurerm_windows_web_app.asvw](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/windows_web_app) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) Access to terraform-conventions module.

Type: `any`

### <a name="input_delegated_subnet_id"></a> [delegated\_subnet\_id](#input\_delegated\_subnet\_id)

Description: (Required) Delegated subnet for app service vnet integration

Type: `string`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Azure resource group name.

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix.

Type: `string`

### <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id)

Description: (Required) Subnet for private endpoints.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_app_service_plan_id"></a> [app\_service\_plan\_id](#input\_app\_service\_plan\_id)

Description: (Optional) Allow using existing App Service plan instead of deploying a dedicated one for each web app. This is the RECOMMENDED way for new deployments.

Type: `string`

Default: `null`

### <a name="input_app_settings"></a> [app\_settings](#input\_app\_settings)

Description: Custom app settings

Type: `map(any)`

Default: `{}`

### <a name="input_auth_settings"></a> [auth\_settings](#input\_auth\_settings)

Description: enabled - (Required) Should the Authentication / Authorization feature be enabled for the Linux Function App?  
active\_directory - (Optional) An active\_directory block as defined above.  
unauthenticated\_client\_action - (Optional) The action to take when an unauthenticated client attempts to access the app. Possible values include: RedirectToLoginPage, AllowAnonymous.

Type:

```hcl
object({
    enabled = bool
    active_directory = optional(object({
      client_id = string
      })
    )
    unauthenticated_client_action = optional(string)

  })
```

Default:

```json
{
  "active_directory": {
    "client_id": ""
  },
  "enabled": true,
  "unauthenticated_client_action": "RedirectToLoginPage"
}
```

### <a name="input_auth_settings_v2"></a> [auth\_settings\_v2](#input\_auth\_settings\_v2)

Description: auth\_enabled - (Required) Should the Authentication / Authorization feature be enabled for the Linux Function App?  
active\_directory\_v2 - (Optional) An active\_directory block as defined below.  
  client\_id - (Required) The ID of the Client to use to authenticate with Azure Active Directory.  
  client\_secret\_setting\_name - (Optional) The App Setting name that contains the client secret of the Client.  
  allowed\_applications - (Optional) The list of allowed Applications for the Default Authorisation Policy.  
  allowed\_audiences - (Optional) Specifies a list of Allowed audience values to consider when validating JWTs issued by Azure Active Directory.  
  allowed\_groups - (Optional) The list of allowed Group Names for the Default Authorisation Policy.  
  allowed\_identities - (Optional) The list of allowed Identities for the Default Authorisation Policy.  
  client\_secret\_certificate\_thumbprint - (Optional) The thumbprint of the certificate used for signing purposes.  
unauthenticated\_client\_action - (Optional) The action to take when an unauthenticated client attempts to access the app. Possible values include: RedirectToLoginPage, AllowAnonymous.

Type:

```hcl
object({
    auth_enabled          = bool
    http_route_api_prefix = optional(string, "/.auth")
    active_directory_v2 = object({
      client_id                            = string
      client_secret_setting_name           = optional(string, "MICROSOFT_PROVIDER_AUTHENTICATION_SECRET")
      allowed_applications                 = optional(list(string), [])
      allowed_audiences                    = optional(list(string), [])
      allowed_groups                       = optional(list(string), [])
      allowed_identities                   = optional(list(string), [])
      client_secret_certificate_thumbprint = optional(string)
      jwt_allowed_client_applications      = optional(list(string), [])
      jwt_allowed_groups                   = optional(list(string), [])
      login_parameters                     = optional(map(any))
    })
    login = optional(object({
      allowed_external_redirect_urls = optional(list(string), [])
      token_store_enabled            = optional(bool, false)
      }),
      ({
        allowed_external_redirect_urls = []
        token_store_enabled            = false
      })
    )
    unauthenticated_action = optional(string, "RedirectToLoginPage")

  })
```

Default: `null`

### <a name="input_auth_version"></a> [auth\_version](#input\_auth\_version)

Description: (Optional) Possible values are v1 or v2.

Type: `string`

Default: `"v1"`

### <a name="input_azurerm_service_plan_sku_name"></a> [azurerm\_service\_plan\_sku\_name](#input\_azurerm\_service\_plan\_sku\_name)

Description: (Optional) The SKU for the plan. Possible values include B1, B2, B3, D1, F1, I1, I2, I3, I1v2, I2v2, I3v2, P1v2, P2v2, P3v2, P1v3, P2v3, P3v3, S1, S2, S3, SHARED, EP1, EP2, EP3, WS1, WS2, WS3, and Y1.

Type: `string`

Default: `null`

### <a name="input_client_certificate_enabled"></a> [client\_certificate\_enabled](#input\_client\_certificate\_enabled)

Description: (Optional) Should Client Certificates be enabled? Defaults to true when http2\_enabled is false. Defaults to false when http2\_enabled is true.

Type: `bool`

Default: `null`

### <a name="input_client_certificate_mode"></a> [client\_certificate\_mode](#input\_client\_certificate\_mode)

Description: (Optional) The Client Certificate mode. Possible values are Required, Optional, and OptionalInteractiveUser. This property has no effect when client\_certificate\_enabled is false. Defaults to Required.

Type: `string`

Default: `"Required"`

### <a name="input_connection_strings"></a> [connection\_strings](#input\_connection\_strings)

Description:     (Optional) One or more connection\_string blocks as defined below.  
      name - (Required) The name which should be used for this Connection.  
      type - (Required) Type of database. Possible values include: APIHub, Custom, DocDb, EventHub, MySQL, NotificationHub, PostgreSQL, RedisCache, ServiceBus, SQLAzure, and SQLServer.  
      value - (Required) The connection string value.

Type:

```hcl
list(object({
    name  = string
    type  = string
    value = string
  }))
```

Default: `null`

### <a name="input_detailed_error_messages"></a> [detailed\_error\_messages](#input\_detailed\_error\_messages)

Description: (Optional) Should detailed error messages be enabled. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_dflt_app_settings"></a> [dflt\_app\_settings](#input\_dflt\_app\_settings)

Description: Mandatory settings for app\_settings

Type:

```hcl
object({
    WEBSITE_DNS_SERVER = string
  })
```

Default:

```json
{
  "WEBSITE_DNS_SERVER": "**********"
}
```

### <a name="input_failed_request_tracing"></a> [failed\_request\_tracing](#input\_failed\_request\_tracing)

Description: (Optional) Should tracing be enabled for failed requests. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_https_only"></a> [https\_only](#input\_https\_only)

Description: (Optional) Should the Linux Web App require HTTPS connections. Defaults to true. Policy exception is required to update this parameter.

Type: `bool`

Default: `true`

### <a name="input_identity_ids"></a> [identity\_ids](#input\_identity\_ids)

Description: (Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Windows Web App. A SystemAssigned identity will always be created.

Type: `list(string)`

Default: `null`

### <a name="input_log_analytics_diag_logs"></a> [log\_analytics\_diag\_logs](#input\_log\_analytics\_diag\_logs)

Description: (Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_metrics"></a> [log\_analytics\_metrics](#input\_log\_analytics\_metrics)

Description: (Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace

Type: `string`

Default: `null`

### <a name="input_pe_wait_after"></a> [pe\_wait\_after](#input\_pe\_wait\_after)

Description: (Optional) Seconds to wait after private endpoint created.

Type: `number`

Default: `120`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Create resource health monitoring alert rule. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_site_config"></a> [site\_config](#input\_site\_config)

Description:     (Required) A site\_config block as defined below.  
      always\_on - (Optional) If this Windows Web App is Always On enabled. Defaults to true.  
                  always\_on must be explicitly set to false when using Free, F1, D1, or Shared Service Plans.  
      vnet\_route\_all\_enabled   - (Optional) Should all outbound traffic to have NAT Gateways, Network Security Groups and User Defined Routes applied? Defaults to true which is the policy requirement at OTP HU.  
      http2\_enabled            - (Optional) Should the HTTP2 be enabled? Defaults to true which is the policy requirement at OTP HU.  
      minimum\_tls\_version      - (Optional) The configures the minimum version of TLS required for SSL requests. Possible values include: 1.0, 1.1, and 1.2. Defaults to 1.2 which is the policy requirement at OTP HU.  
      remote\_debugging\_enabled - (Optional) Should Remote Debugging be enabled. Defaults to false which is the policy requirement at OTP HU.  
      container\_registry\_managed\_identity\_client\_id - (Optional) The Client ID of the Managed Service Identity to use for connections to the Azure Container Registry.  
      container\_registry\_use\_managed\_identity       - (Optional) Should connections for Azure Container Registry use Managed Identity.  
      health\_check\_path - (Optional) The path to the Health Check.  
      health\_check\_eviction\_time\_in\_min - (Optional) The amount of time in minutes that a node can be unhealthy before being removed from the load balancer. Possible values are between 2 and 10. Only valid in conjunction with health\_check\_path.  
      An application\_stack block supports the following:  
        current\_stack       - (Optional) The Application Stack for the Windows Web App. Possible values include dotnet, dotnetcore, node, python, php, and java.  
        dotnet\_version      - (Optional) The version of .NET to use when current\_stack is set to dotnet. Possible values include v2.0,v3.0, v4.0, v5.0, v6.0, v7.0 and v8.0.  
        dotnet\_core\_version - (Optional) The version of .NET to use when current\_stack is set to dotnetcore. Possible values include v4.0.  
        tomcat\_version      - (Optional) The version of Tomcat the Java App should use. Conflicts with java\_embedded\_server\_enabled  
        java\_embedded\_server\_enabled - (Optional) Should the Java Embedded Server (Java SE) be used to run the app.  
        java\_version        - (Optional) The version of Java to use when current\_stack is set to java.  
        node\_version        - (Optional) The version of node to use when current\_stack is set to node. Possible values are ~12, ~14, ~16, and ~18.  
        php\_version         - (Optional) The version of PHP to use when current\_stack is set to php. Possible values are 7.1, 7.4 and Off.  
        docker\_image\_name   - (Optional) The docker image, including tag, to be used. e.g. appsvc/staticsite:latest.  
        docker\_registry\_url - (Optional) The URL of the container registry where the docker\_image\_name is located. e.g. https://index.docker.io or https://mcr.microsoft.com. This value is required with docker\_image\_name.  
        docker\_registry\_username - (Optional) The user name to use for authentication against the registry to pull the image.  
        docker\_registry\_password - (Optional) The password to use for authentication against the registry to pull the image. Do not pass password in clear text format.

Type:

```hcl
object({
    vnet_route_all_enabled   = optional(bool,true)
    http2_enabled            = optional(bool,true)
    minimum_tls_version      = optional(string,"1.2")
    always_on                = optional(bool)
    health_check_path        = string
    health_check_eviction_time_in_min = optional(number)
    remote_debugging_enabled = optional(bool,false)
    container_registry_managed_identity_client_id = optional(string)
    container_registry_use_managed_identity       = optional(bool)    
    application_stack = optional(object({
      current_stack                = optional(string)
      dotnet_version               = optional(string)
      dotnet_core_version          = optional(string)
      tomcat_version               = optional(string)
      java_embedded_server_enabled = optional(string)
      java_version                 = optional(string)
      node_version                 = optional(string)
      php_version                  = optional(string)
      docker_image_name            = optional(string)
      docker_registry_url          = optional(string)
      docker_registry_username     = optional(string)
      docker_registry_password     = optional(string)      
      }
    ))
  })
```

Default:

```json
{
  "health_check_path": "/",
  "http2_enabled": true,
  "minimum_tls_version": "1.2",
  "remote_debugging_enabled": false,
  "vnet_route_all_enabled": true
}
```

### <a name="input_storage_accounts"></a> [storage\_accounts](#input\_storage\_accounts)

Description:   (Optional) List object to define one or more storage accounts to connect to web-app. Storage account must be created outside of web-app module.  
    stac\_object  - (Required) Storage account object what you would like to connect (storage account must have shared key enabled)  
    share\_name   - (Required) The Name of the File Share or Container Name for Blob storage  
    type         - (Optional) The Azure Storage Type. Possible values include AzureFiles and AzureBlob. Defaults to AzureFiles.  
    mount\_path   - (Optional) The path at which to mount the storage share.

Type:

```hcl
list(object({
    stac_object  = any
    share_name   = string
    type         = optional(string)
    mount_path   = optional(string)
  }))
```

Default: `[]`

### <a name="input_vnet_backup_restore_enabled"></a> [vnet\_backup\_restore\_enabled](#input\_vnet\_backup\_restore\_enabled)

Description: (Optional) To enable backup-restore over virtual network. Defaults to true to comply with Azure policies.

Type: `bool`

Default: `true`

### <a name="input_vnet_content_share_enabled"></a> [vnet\_content\_share\_enabled](#input\_vnet\_content\_share\_enabled)

Description: (Optional) To enable accessing content over virtual network. Defaults to true to comply with Azure policies.

Type: `bool`

Default: `true`

### <a name="input_vnet_image_pull_enabled"></a> [vnet\_image\_pull\_enabled](#input\_vnet\_image\_pull\_enabled)

Description: (Optional) To enable pulling image over virtual network. Defaults to true to comply with Azure policies.

Type: `bool`

Default: `true`

### <a name="input_windows_web_app_tags"></a> [windows\_web\_app\_tags](#input\_windows\_web\_app\_tags)

Description: (Optional) Tags to apply to the Windows Web App

Type: `map(string)`

Default: `null`

### <a name="input_worker_count"></a> [worker\_count](#input\_worker\_count)

Description: (Optional) The number of Workers (instances) to be allocated. Defaults to 2.

Type: `number`

Default: `2`

## Outputs

The following outputs are exported:

### <a name="output_aasp"></a> [aasp](#output\_aasp)

Description: Azure App Service Plan Object (Windows Version)

### <a name="output_asvw"></a> [asvw](#output\_asvw)

Description: Azure Web App Service Object (Windows Version), contains sensitive data.

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->