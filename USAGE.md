<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure AI Foundry Module

shortname: aihu

terraform resources created by this module:
- azapi\_resource/Microsoft.MachineLearningServices/workspaces
- azapi\_resource/Microsoft.CognitiveServices/accounts
- azapi\_resource/Microsoft.MachineLearningServices/workspaces/connections

## Short description of the module
This Terraform module deploys Azure AI Foundry HUB and project.

## Detailed description on Confluence
[Azure AI Foundry](https://confluence.otpbank.hu/x/uxAPQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 3.110.0
provider registry.terraform.io/Azure/azapi >= 2.1.0

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- Azure AI Foundry HUB
- Azure AI Project
- Private endpoints

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.24.0)

- <a name="requirement_time"></a> [time](#requirement\_time) (>= 0.9.1)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.24.0)

## Example for Provider configuration

```hcl
#provider - All provider configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    key_vault {
      purge_soft_delete_on_destroy               = true
      recover_soft_deleted_key_vaults            = true
      purge_soft_deleted_certificates_on_destroy = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.41.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.13.1"
    }
  }
}

provider "azurerm" {
  features {}
  alias           = "logging"
  subscription_id = "a3a538d4-3d2b-4b05-b268-f147b8788b86"
}

```

## Example for Convention

```hcl
module "conventions" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.3"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  subsidiary  = var.subsidiary
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for Data request

```hcl
# Subnet
data "azurerm_subnet" "sn_privateendpoint" {
  name                 = var.snet_name
  virtual_network_name = var.vnet_name
  resource_group_name  = var.vnet_rgrp_name
}

data "azurerm_key_vault" "default" {
  name                = var.kvau_name
  resource_group_name = var.kvau_rgrp_name
}

```

## Example for AI HUB and Project creation

```hcl
locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "aifoun"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}


# Resource Group
module "rg01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.1"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

resource "azurerm_user_assigned_identity" "foundry" {
  location            = module.conventions.region
  name                = "umid-weu-${module.conventions.environment}-foundry-${local.suffix}"
  resource_group_name = module.rg01.rgrp.name
}

resource "azurerm_role_assignment" "umid_kv_enc" {
  scope                = data.azurerm_key_vault.default.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.foundry.principal_id
}
resource "time_sleep" "role_assignment_wait" {
  depends_on      = [azurerm_role_assignment.umid_kv_enc]
  create_duration = "120s"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "aifoundrykey" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.8.1"
  conventions     = module.conventions
  key_name        = "testkey0${local.suffix}"
  key_vault_id    = data.azurerm_key_vault.default.id
  key_type        = "RSA"
  key_size        = 4096
  key_opts        = ["encrypt", "wrapKey", "unwrapKey", "decrypt", "sign"]
  expiration_date = time_offset.expirationdate.rfc3339
}


##### END OF PREREQUISITES
###########################

module "aifoundry01" {
  source = "../.."
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-foundry?ref=v2.0.0"
  resource_name_suffix = local.suffix
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id

  identity_ids = [azurerm_user_assigned_identity.foundry.id]
  customer_managed_key = {
    identity_client_id = azurerm_user_assigned_identity.foundry.client_id
    key_vault_key_id   = module.aifoundrykey.azurerm_key_vault_key.id
  }

  log_analytics_diag_logs = ["AllLogs"]
  log_analytics_metrics   = ["AllMetrics"]
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id

  aifoundry_sku                = "S0"
  aifoundry_custom_domain_name = var.environment == "prd" ? "otpprd${local.suffix}" : "otpnonprod${local.suffix}"
  depends_on                   = [module.rg01, time_sleep.role_assignment_wait]
}

```




## Resources

The following resources are used by this module:

- [azurerm_ai_services.aifoundry](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/ai_services) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_customer_managed_key"></a> [customer\_managed\_key](#input\_customer\_managed\_key)

Description:   (Required) A customer\_managed\_key block supports the following:  
    key\_vault\_key\_id - (Required) The ID of the Key Vault Key which should be used to Encrypt the data in this Cognitive Account.  
    user\_assigned\_identity\_id - (Required) The Client ID of the User Assigned Identity that has access to the key.

Type:

```hcl
object({
    key_vault_key_id   = string
    identity_client_id = string
  })
```

### <a name="input_identity_ids"></a> [identity\_ids](#input\_identity\_ids)

Description: (Required) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Cognitive Service. A SystemAssigned identity will always be created.

Type: `list(string)`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) The name of the resource group in which to create the storage account.

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id)

Description: (Required) ID of the subnet where private endpoint should be created.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_acr_id"></a> [acr\_id](#input\_acr\_id)

Description: (Optional) ID of the container registry associated with this workspace.

Type: `string`

Default: `null`

### <a name="input_ai-foundry_tags"></a> [ai-foundry\_tags](#input\_ai-foundry\_tags)

Description: (Optional) Tags to apply to AI foundry service

Type: `map(string)`

Default: `null`

### <a name="input_aifoundry_custom_domain_name"></a> [aifoundry\_custom\_domain\_name](#input\_aifoundry\_custom\_domain\_name)

Description: (Optional)	Optional subdomain name used for token-based authentication.

Type: `string`

Default: `null`

### <a name="input_aifoundry_sku"></a> [aifoundry\_sku](#input\_aifoundry\_sku)

Description: (Required) The name of the SKU. Ex - P3. It is typically a letter+number code.

Type: `string`

Default: `"S0"`

### <a name="input_alert_SuccessRate_threshold"></a> [alert\_SuccessRate\_threshold](#input\_alert\_SuccessRate\_threshold)

Description: (Optional) Threshold for Availability Rate alert rule.

Type: `number`

Default: `90`

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: (Optional) Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_log_analytics_diag_logs"></a> [log\_analytics\_diag\_logs](#input\_log\_analytics\_diag\_logs)

Description: (Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_metrics"></a> [log\_analytics\_metrics](#input\_log\_analytics\_metrics)

Description: (Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace

Type: `string`

Default: `null`

### <a name="input_network_acl_bypass"></a> [network\_acl\_bypass](#input\_network\_acl\_bypass)

Description: (Optional) Specifies which traffic can bypass the network rules. Possible values are AzureServices and None.

Type: `string`

Default: `"None"`

### <a name="input_network_isolation_mode"></a> [network\_isolation\_mode](#input\_network\_isolation\_mode)

Description: (Optional) The isolation mode of the Machine Learning Workspace. Possible values are Disabled, AllowOnlyApprovedOutbound, and AllowInternetOutbound

Type: `string`

Default: `"AllowOnlyApprovedOutbound"`

### <a name="input_outbound_network_access_restricted"></a> [outbound\_network\_access\_restricted](#input\_outbound\_network\_access\_restricted)

Description: (Optional) Whether outbound network access is restricted for the AI Services Account. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_public_network_access_enabled"></a> [public\_network\_access\_enabled](#input\_public\_network\_access\_enabled)

Description: (Optional) Whether public network access is allowed for the AI Services Account. Possible values are true and false. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_virtual_network_rules"></a> [virtual\_network\_rules](#input\_virtual\_network\_rules)

Description:    (Optional) List of virtual network rules to apply to the AI Services Account.  
   subnet\_id - (Required) The ID of the subnet which should be able to access this AI Services Account.  
   ignore\_missing\_vnet\_service\_endpoint - (Optional) Whether to ignore a missing Virtual Network Service Endpoint or not. Default to false.

Type:

```hcl
list(object({
    subnet_id                            = string
    ignore_missing_vnet_service_endpoint = bool
  }))
```

Default: `[]`

## Outputs

The following outputs are exported:

### <a name="output_aifo"></a> [aifo](#output\_aifo)

Description: Azure AI Foundry

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->