## Windows VM module
### v3.1.1 - April 15, 2025 [current]
BUGFIXES:
- CCE-7900 - Fix VNIC recreation issue

### v3.1.0 - March 28, 2025 [deprecated]
ENHANCEMENTS:
- CCE-7851 - Support for external VNIC association

### v3.0.0 - December 18, 2024
ENHANCEMENTS:
- Raised minimum azapi provider version to 2.1.0 - CCE-7582

### v2.1.0 - December 16, 2024
FEATURES
- New variable to support Terraform state refresh after swapping OS disk: os_disk_name_override
- Release is tested with azurerm v4.10.0 and azapi v1.15.0

### v2.0.0 - December 10, 2024
ENHANCEMENTS:
- Updated alerting module version to support deployment in regions other than West Europe (optional variable can be used to override default behaviour: resource_health_alert_location)
- Updated pipelines to use v7 pipeline templates
- For collecting event or perf counter data into shared log analytics workspace in GWC region, conventions module v2.1.0 or higher must be used
- Release is tested with azurerm v4.10.0 and azapi v1.15.0
- AZAPI provider minimum version raised to v1.13.1 to support federated connections. IMPORTANT NOTE: Current version is not compatible with azapi v2.x

### v1.5.0 - Sept 11, 2024
ENHANCEMENTS:
- Added azurerm v4.x support.

### v1.4.0 - July 23, 2024
FEATURES:
- New variable to support configuring a VM as an Application Gateway backend: appgw_backend_address_pool_id, appgw_vnic_ip_configuration_name

ENHANCEMENTS:
- Release is tested with azurerm v3.86.0 and v3.101.0

### v1.3.0 - May 12, 2024
FEATURES:
- Auto-shutdown schedule can be enabled by using auto_shutdown variable.

ENHANCEMENTS:
- Testing redesign changes: updated examples and new pipelines

### v1.2.0 - March 12, 2024 
FEATURES:
- Removed option to pass administrator password directly. Removed variable admin_password (Kanncellar finding CCE-5771)
- Initial administrator password can be set indirectly via new variables: key_vault_id, admin_secret_name
    - Dependency to secret creation must be configured at vm creation (depends_on = [ module.secret ])

ENHANCEMENTS:
- Removed 4.0.0 provider limitation

### v1.1.0 - February 15, 2024
ENHANCEMENTS:
- New tag (EnablePrivateNetworkGC) is required for the proper working of Azure policy's machine configuration feature. It can be configured with variable guest_config_enabled.

### v1.0.0 - January 23, 2024 [deprecated]
FEATURES:
- az  network watcher extension installation added to module

ENHANCEMENTS:
- Prepared module for v1.0.0 release
- Updated module versions

### v0.6.16 - January 17, 2024
ENHANCEMENTS:
- sccm_group variable is validated for acceptable group names
- VM name and computer name can be overwritten by vmname_override and computer_name_override parameters - this is required to support AVD resource naming conventions (
    - New parameters should be used only from AVD module

### v0.6.15 - January 4, 2024
FEATURES:
- Added variable disk_encryption_set_id to allow encryption configuration on OS disk
- Added variable storage_account_type to allow OS disk type configuration
- Public network access is disabled on OS disk

ENHANCEMENTS:
- Identity can be configured. It defaults to SystemAssigned. Two variables added: identity_type, identity_ids
- Data Collection Rule is automatically assigned to a shared Data Collection Endpoint.

### v0.6.14 - December 5, 2023
ENHANCEMENTS:
- Admin password creation has been removed from the module.
- Admin password need to be created as prerequisite before vm module call - see examples.
- Variable admin_password need to be used to pass the value to the module.
- Added new variables domain_name and sccm_group to support domain join infrastructure. If these variables are set to empty string domain join is not going to happen.

### v0.6.13 - November 28, 2023
FEATURES:
- Allow custom tagging and add new default tag about TF module version (locals moved to dedicated locals.tf)

ENHANCEMENTS:
- Variables added to allow disabling Azure Monitor Agent and DCE association. These options might be required if user wants to let Azure policies to perform these operations.

### v0.6.12 - November 2, 2023
- Updated metrics according to AMBA guidelines, JIRA CCE-4490
- Updated module versions
- Maximum provider version is set to < 4.0.0 (tested on provider version 3.78.0)
- Disk attachment process changed to support disk removal and disk addition without side effect
- Support Ultra SSD disk addition via ultra_ssd_enabled parameter (by default false)

### v0.6.11 - October 27, 2023
- Turned off secure boot.
- Changed image sourcing

### v0.6.10 - October 10, 2023
- License type variable (Hybrid benefit) set to required by default.

### v0.6.9 - October 10, 2023
- Event log collection feature added
- Event log data can be sent to shared or own log analytics workspace
- Use eventlog_settings and log_analytics_workspace_id parameters to configure log collection

### v0.6.8 - September 21, 2023
- Now it is possible to attach one or more attached disks to the VM (example in 01-default folder)
- Submodule versions were updated
- Set azurerm provider max version to 3.70.0 because of sporadic issues

### v0.6.7 - September 19, 2023
- Alerting resource naming update to avoid name conflicts

### v0.6.6 - September 14, 2023
- It is possible to attach one managed disk to VM

### v0.6.5 - September 1, 2023
- Resource health monitoring added
- Initial metric set added

### v0.6.4 - August 17, 2023
- Reverting back the 3 examples
- Using proper image gallery
- Add provider to examples

### v0.6.3 - August 14, 2023
- Adding only Market place image support

### v0.6.2 - June 15, 2023
- applied new vm naming

### v0.6.1 - June 14, 2023
- source links were inserted as comments
- checkov remediation was done
- enabled encryption at host

### v0.6.0 - June 07, 2023
- Rearranged folders to adhere to new SBB standards
- Added azure-pipelines folder to contain all old and new validation and test automation pipelines: terraform-checkov, terraform-module-semantic-validate, terraform-module-terraform-docs and terratest
- Added anchor.module file to serve validation and test automation pipelines
- Added "Terratest" test automation feature and the necessary files and folders
- Renamed examples to adhere to Terratest requirements
- Updated versions of included modules
- vnic creation was moved into the module, tests were refactored
- Hybrid benefit was NOT enabled, but it was tested and it is possible to enable it now easily
- zone variable is optional (tricky but it works, comment included)
- Boot diagnostic is enabled with Azure managed disk

### v0.5.1 - March 23, 2023
- Adding modification to use image gallery

### v0.5.0 - March 03, 2023
- Added support for compute gallery images