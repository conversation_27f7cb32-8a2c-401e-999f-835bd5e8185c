## Azure Windows Web App Service Module
### v4.0.1 - March 06, 2025 [current]
BUG FIXES:
- Fixed EntraId auth V2 endpoint format - CCE-7796

### v4.0.0 - Dec 30, 2024
ENHANCEMENTS:
- Raised minimum azapi provider version to 2.1.0 - CCE-7586

### v3.0.0 - Dec 11, 2024
FEATURES:
- New variable to support separated app service plan creation and/or creating new web app in existing app service plan, variable name: app_service_plan_id
    - IMPORTANT NOTE - BREAKING CHANGE: This module version is NOT COMPATIBLE with resourced deployed with earlier versions of this module. Existing app service plan and web app resources MUST BE DESTROYED before using this new version.
    - azurerm_service_plan_sku_name variable must not be set when app_service_plan_id is set app_service_plan_id is not null
    - Builtin App service plan is created only when azurerm_service_plan_sku_name defined

ENHANCEMENTS:
- Updated alerting module version to support deployment in regions other than West Europe (optional variable can be used to override default behaviour: resource_health_alert_location)
- Updated pipelines to use v7 pipeline templates
- Release is tested with azurerm v4.10.0 and azapi v1.15.0
- AZAPI provider minimum version raised to v1.13.1 to support federated connections. IMPORTANT NOTE: Current version is not compatible with azapi v2.x

BUG FIXES:
- Corrected variable descriptions

### v2.0.0 - September 16, 2024
FEATURES:
- Standardization of usage of optional parameters
    - Hardcoded property is exposed to variables to allow changes with policy exception: https_only
    - New variable to allow configuring client certificate mode: client_certificate_mode
    - Client_certificate_enabled is updated to be set as per http2 configuration - same logic used in Linux function app module but default can be overwritten by client_certificate_enabled variable
- New variable site_config.health_check_eviction_time_in_min which valid in conjunction with health_check_path
- Introduced three new variables (all defaults to true) to comply with Kancellar policy requirement: vnet_image_pull_enabled, vnet_content_share_enabled, vnet_backup_restore_enabled (CCE-5505)
- AZAPI provider is added to required provider list
- Added connection_strings variable
- Added auth_version (defaults to v1 for backward compatibility) and auth_settings_v2 variable, Jira ticket: CCE-6539
- Allow worker_count to be set to 1 (for dev purposes)
- Updated azurerm minimum prodivder version to v4.0.1
- Release is tested with azurerm v4.0.1

### v1.2.0 - May 24, 2024
FEATURES:
- Support container image based deployment by new variables:
    - site_config.container_registry_managed_identity_client_id
    - site_config.container_registry_use_managed_identity
    - site_config.application_stack.docker_image_name
    - site_config.application_stack.docker_registry_url
    - site_config.application_stack.docker_registry_username
    - site_config.application_stack.docker_registry_password
- Added new example to demonstrate container based deployment (02-webapp_from_image)
- Added .config file to enable new release pipeline to run
- Added solution to test module in tst and prd environments and new execute pipeline
- Updated module testing to use Terraform test and added new pipelines. This test is using Terraform 1.7.4.
- Increased azurerm provider version to 3.75

### v1.1.0 - March 04, 2024
FEATURES:
- Introduced variable failed_request_tracing to support logging configuration
- Introduced variable detailed_error_messages to support logging configuration
- Storage account creation is moved out from module
- Optionally one or more storage accounts can be passed to the module via storage_accounts list object variable
- Variable remote_debugging_enabled added to site_config object variable with default false value to comply with Kancellar recommendation
- wait_after variable renamed to pe_wait_after
- Module now using the iac-terratest pipeline for testing, and the central iac-common pipeline for execute
- Module now using the az-nexus repo for providers via the network-mirror/.terraformrc configuration

ENHANCEMENTS:
- README.md updated with provider information
- Updated module and provider versions


### v1.0.0 - October 05, 2023
- Module prepared for release v1.0.0
- Updated module versions
- Updated provider versions
- Added Checkov skip
### v0.3.0 - September 19, 2023
- Testing refactor
- vnet, site config support
- Added logging
- Added resource monitor
- Added Checkov skips
- Integrated stac, aasp modules
### v0.2.0 - March 29, 2023
- Added naming convention feature to ensure globally unique naming.
- Updated versions of included modules
- Added absolute source path to tests
- Added ChangeLog.md
### v0.1.0
- fixed naming conventions