## AI Foundry Module
### v2.0.0 - Sept 02, 2025 [current]
- Implement CCE-8321
FEATURES:
- Removed HUB, Project
- Added CMK, HSM support
- Added network features 
- Changed from azapi to azurerm

### v1.0.2 - Aug 25, 2025
BUGFIXES:
- Added explicit dependencies for private endpoint creation to resolve timing issues with AI Hub workspace provisioning - CCE-8242

### v1.0.1 - Jun 11, 2025
BUGFIXES:
- Fix versioning in CHANGELOG and throughout the module
- Removed "ApiProperties" block & "statisticsEnabled" property because it's only used for special AI Resource types e.g.: Bing Search. It caused deployment errors.
- Increased Microsoft.CognitiveServices/accounts API version to latest non-preview @2024-10-01
- Increased Microsoft.MachineLearningServices/workspaces API version to latest supported

### v1.0.0 - May 20, 2025
FEATURES:
- Release v1.0.0
ENHANCEMENTS:
- Raised minimum azapi provider version to 2.1.0 - CCE-7580
CHORES:
- Renamed module to AI Foundry from AI Studio - CCE-7837

### v0.3.0 - Dec 10, 2024
ENHANCEMENTS:
- Updated alerting module version to support deployment in regions other than West Europe (optional variable can be used to override default behaviour: resource_health_alert_location)
- Updated pipelines to use v7 pipeline templates
- Release is tested with azurerm v4.10.0

### v0.2.0 - September 30, 2024
ENHANCEMENTS:
- Module updates to support v4.x azurerm provider
- azurerm minimum provider version changed to v3.110.0
- Release is tested with azurerm v3.110.0 and v4.3.0

### v0.1.0 - August 27, 2024
FEATURES:
- Initial version of AI Studio module