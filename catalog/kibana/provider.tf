terraform {
  required_version = "~> 1.3.6"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = ">= 3.30.0, < 4.0.0"
    }
  }
}

# Configure the Microsoft Azure Provider
provider "azurerm" {
  features {}

  subscription_id = "52953d73-d162-4fb2-a5d3-b004a6a84781"
  tenant_id = "60c1c779-9336-42ce-8e98-772a5e8de926"
  client_id = "e153c748-5cc7-4d66-86d9-d6acbb84bdc6"
}

provider "helm" {
  kubernetes {
    host                   = data.azurerm_key_vault_secret.aksc-fqdn.value
    cluster_ca_certificate = base64decode(data.azurerm_key_vault_secret.cert-aksc-ca.value)
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      args = [
        "./aks_login.sh",
        "e153c748-5cc7-4d66-86d9-d6acbb84bdc6", // SP Application ID
        "60c1c779-9336-42ce-8e98-772a5e8de926"  // Tenant ID
      ]
      command = "bash"
    }
  }
}
