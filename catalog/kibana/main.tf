module "conventions" {
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v0.5.7"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = "<EMAIL>"
    "Criticality"  = "false"
  }
}

locals {
  name_aksc = "aksc-weu-dev-akstest01"
}

data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name = "OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01"
}

data "azurerm_kubernetes_cluster" "aksc" {
  name = local.name_aksc
  resource_group_name = "rgrp-weu-dev-akstest01"
}

data "azurerm_key_vault_secret" "cert-aksc-ca" {
  name         = "${local.name_aksc}-ca-certificate"
  key_vault_id = data.azurerm_key_vault.vault.id  
}

data "azurerm_key_vault_secret" "aksc-fqdn" {
  name         = "${local.name_aksc}-fqdn"
  key_vault_id = data.azurerm_key_vault.vault.id
}

data "azurerm_key_vault_secret" "acre-password" {
  name         = "acreweudevakstest01aks-admintoken"
  key_vault_id = data.azurerm_key_vault.vault.id
}

module "nexus2acr" {
  source        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-tools-nexus2acr?ref=v0.1.0"
  acr_name      = "acreweudevakstest01aks"
  acr_user      = "admintoken"
  acr_password  = data.azurerm_key_vault_secret.acre-password.value
  source_images = ["anonymous-proxy-do-docker-elastic-co.otpnexus.hu/kibana/kibana:8.5.1"]
}

resource "helm_release" "kibana" {
  depends_on = [ module.nexus2acr ]
  name    = "kibana"
  timeout = 1500
  chart   = "../../helm/kibana/v8.5.1"
  wait    = true
  namespace = "monitoring"
  create_namespace = true
  set {
    name  = "image"
    value = "acreweudevakstest01aks.azurecr.io/kibana/kibana"
  }
  set {
    name = "ingress.enabled"
    value = "true"
  }
  set {
    name = "ingress.hosts[0].host"
    value = "aksc-weu-dev-akstest01.coeinfdev.dev.privatelink.westeurope.azmk8s.io"
  }
  set {
    name = "ingress.hosts[0].paths[0].path"
    value = "/kibana"
  }
  set {
    name = "kibanaConfig.kibana\\.yml"
    value = "server.basePath: \"/kibana\"\nserver.rewriteBasePath: \"false\""
  }
  set {
    name = "healthCheckPath"
    value = "/login"
  }
}

