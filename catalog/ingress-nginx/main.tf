module "conventions" {
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v0.5.7"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = "<EMAIL>"
    "Criticality"  = "false"
  }
}

locals {
  name_aksc = "aksc-weu-dev-akstest01"
  acr_name = "acreweudevakstest01aks"
  ingress_class = "secondary"
}

data "azurerm_kubernetes_cluster" "aksc" {
  name = local.name_aksc
  resource_group_name = "rgrp-weu-dev-akstest01"
}

data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name = "OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01"
}
/*
data "azurerm_key_vault_secret" "cert-aksc-ca" {
  name         = "${local.name_aksc}-ca-certificate"
  key_vault_id = data.azurerm_key_vault.vault.id  
}

data "azurerm_key_vault_secret" "aksc-fqdn" {
  name         = "${local.name_aksc}-fqdn"
  key_vault_id = data.azurerm_key_vault.vault.id
}
*/
data "azurerm_key_vault_secret" "acre-password" {
  name         = "acreweudevakstest01aks-admintoken"
  key_vault_id = data.azurerm_key_vault.vault.id
}

module "nexus2acr" {
  source        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-tools-nexus2acr?ref=v0.1.0"
  acr_name      = local.acr_name
  acr_user      = "admintoken"
  acr_password  = data.azurerm_key_vault_secret.acre-password.value
  source_images = [
    "anonymous-proxy-do-registry-1-docker-io.otpnexus.hu/otpbankhu/ingress-nginx_controller:v1.7.1",
    "anonymous-proxy-do-registry-1-docker-io.otpnexus.hu/otpbankhu/ingress-nginx_kube-webhook-certgen:v20230312-helm-chart-4.5.2-28-g66a760794",
    "anonymous-proxy-do-registry-1-docker-io.otpnexus.hu/otpbankhu/defaultbackend-amd64:1.5"
    ]
}

resource "helm_release" "ingress" {
  depends_on = [ module.nexus2acr ]
  name    = "ingress-${local.ingress_class}"
  timeout = 600
  chart   = "../../helm/ingress-nginx/v4.6.1"
  wait    = true
  namespace = "ingress-${local.ingress_class}"
  create_namespace = true
  reuse_values = true
  // Container images
  set {
    name  = "controller.image.registry"
    value = "${local.acr_name}.azurecr.io"
  }
  set {
    name  = "controller.image.image"
    value = "otpbankhu/ingress-nginx_controller"
  }
  set {
    name  = "controller.image.digest"
    value = ""
  }
  set {
    name  = "controller.admissionWebhooks.patch.image.registry"
    value = "${local.acr_name}.azurecr.io"
  }
  set {
    name  = "controller.admissionWebhooks.patch.image.image"
    value = "otpbankhu/ingress-nginx_kube-webhook-certgen"
  }
  set {
    name  = "controller.admissionWebhooks.patch.image.digest"
    value = ""
  }
  set {
    name  = "defaultBackend.image.registry"
    value = "${local.acr_name}.azurecr.io"
  }
  set {
    name  = "defaultBackend.image.image"
    value = "otpbankhu/defaultbackend-amd64"
  }
  // Load balancer
  set {
    name  = "controller.service.loadBalancerIP"
    value = "**********"
  }
  set {
    name  = "service.type"
    value = "ClusterIP"
  }
  // Annotations
  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/azure-load-balancer-health-probe-request-path"
    value = "/healthz"
  }
  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/azure-load-balancer-internal"
    value = "true"
  }
  set {
    name = "controller.service.externalTrafficPolicy"
    value= "Local"
  }
  set {
    name = "controller.ingressClassResource.name"
    value = local.ingress_class
  }
  set {
    name = "controller.ingressClass"
    value = local.ingress_class
  }
}

