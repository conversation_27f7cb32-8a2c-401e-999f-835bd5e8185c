trigger:
- none

# variables:
#   no_proxy: " " #*.core.windows.net

parameters:
- name: no_proxy
  type: string
  default: ' '

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: centralagent

stages:

  - stage: Terratest
    jobs:
      - template: pipelines/terratest.yaml@tooling
        parameters:
          no_proxy: ${{ variables.no_proxy }}
          timeout_in_minutes: 90