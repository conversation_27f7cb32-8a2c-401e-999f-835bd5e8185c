trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: DEV-demo01
  values:
  - DEV-demo01
  - DEV-coeinf
  - TST-iac
  - PRD-iac

- name: example
  type: string
  default: examples/01-default
  values:
  - examples/01-default
  - examples/01-default-GWC
  - examples/02-aks2
  - examples/11-aks-with-subnet
  - examples/12-aks-with-builtin-insights
  - examples/13-aks-with-nodepool
  - examples/14-aks-with-cmk
  - examples/16-aks-with-nodepool-subnet
  - examples/17-aks-overlay

- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy
    - test

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 120

- name: no_proxy
  type: string
  default: ' '

- name: terraform_version
  type: string
  default: 1.7.4
  values:
  - 1.3.10
  - 1.7.4

- name: terraformUnlockStateLockID
  type: string
  default: ' '

- name: terraformLogLevel
  type: string
  default: 'ERROR' 

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v4
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    action: ${{ parameters.action }}
    environment: ${{ parameters.environment }}
    terraformProjectLocation: ${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    terraformLogLevel: ${{ parameters.terraformLogLevel }}
    skipCheckovScan: ${{ parameters.skip_checkov }}
    appCode: ${{ variables.appCode }}
    armServiceConnectionName: ${{ variables.armServiceConnectionName }}
    storageAccountResourceGroup: ${{ variables.storageAccountResourceGroup }}
    storageAccountName: ${{ variables.storageAccountName }}
    storageAccountContainerName: ${{ variables.storageAccountContainerName }}
    terraformVersion: ${{ parameters.terraform_version }}
