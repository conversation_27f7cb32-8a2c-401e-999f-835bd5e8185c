#Conventions
cloud       = "azure"
environment = "tst"
region      = "germanywestcentral"
project     = "iac"
subsidiary  = "otphq"

//Subnet for private endpoint 
snet_name      = "snet-gwc-pe01"
vnet_name      = "vnet-gwc-tst-iac-01"
vnet_rgrp_name = "rgrp-gwc-tst-iac-01"

//MHSM
mhsm_umid_name      = "umid-gwc-tst-iac-01"
mhsm_umid_rgrp_name = "rgrp-gwc-tst-iac-01"
mhsm_key            = "https://mhsm-gwc-prd-mhsm-01.managedhsm.azure.net/keys/iactst-hsm-01/de35a4c752a50c9f0366b780b385c479"