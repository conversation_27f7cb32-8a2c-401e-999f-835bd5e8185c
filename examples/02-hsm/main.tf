locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "aifoun"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}

# Resource Group
module "rg01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.1"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

##### END OF PREREQUISITES
###########################

module "aifoundry01" {
  source = "../.."
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-foundry?ref=v2.0.0"
  resource_name_suffix = local.suffix
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id

  identity_ids = [data.azurerm_user_assigned_identity.hsm.id]
  customer_managed_key = {
    key_vault_key_id   = var.mhsm_key
    identity_client_id = data.azurerm_user_assigned_identity.hsm.client_id
  }

  aifoundry_sku                = "S0"
  aifoundry_custom_domain_name = var.environment == "prd" ? "otpprd${local.suffix}" : "otpnonprod${local.suffix}"
}
