//////////////////////////////////////////////////////////////////////
// NOTE: Using marketplace images are not allowed in OTP environment!
//////////////////////////////////////////////////////////////////////

variable "owner" {}

locals {
  project       = "wvm"
  index         = "03"
  suffix        = "${local.project}-${local.index}"
  resource_name = "${module.conventions.short_region}-${module.conventions.environment}-${local.suffix}"
}

module "conventions" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v1.1.0"
  cloud       = "azure"
  project     = local.project
  environment = "dev"
  region      = "westeurope"
  tags        = { 
    OwnerOU = "ccoe"
    OwnerContact = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    Criticality = "non-critical"
  }
}

// Generate admin password and add it to key vault
module "password" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//password?ref=v1.1.0"
}

module "secret" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-secret?ref=v1.2.0"
  conventions     = module.conventions
  key_vault_id    = data.azurerm_key_vault.kv.id
  name            = local.resource_name
  value           = module.password.password
  expiration_date = "2024-05-30T12:00:00Z"
}

module "rg" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.1.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

data "azurerm_virtual_network" "vnet" {
  name                = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

data "azurerm_route_table" "rtbl" {
  name                = "otp-dd-coeinfdev-sub-dev-01-rt-westeu-01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

module "subnet_tools" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet-tools?ref=v1.2.4"
  //source                 = "../.."
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  subnet_prefixes_length = [29]
}

module "subnet" {
  #checkov:skip=CKV2_AZURE_31:False alert, We are creating NSG in the example and assigning it to the created subnet
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet?ref=v1.2.4"
  conventions            = module.conventions
  resource_name_suffix   = local.suffix
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  address_prefix         = module.subnet_tools.next_subnets[0]
  associated_route_table = data.azurerm_route_table.rtbl.id
  nsg_needed             = true
  associated_nsg         = module.nesg_01.nesg.id
}

module "nesg_01" {
  #checkov:skip=CKV2_AZURE_31:False alert, We are creating NSG in the example and assigning it to the created subnet
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//nesg?ref=v1.2.4"
  conventions          = module.conventions
  resource_name_suffix = "nesgtst"
  resource_group_name  = module.rg.rgrp.name
}

data "azurerm_key_vault" "kv" {
  name                = "kvau-weu-dev-shared"
  resource_group_name = "rgrp-weu-dev-kvau01"
}

data "azurerm_subnet" "sn_privateendpoint" {
  name                 = "privateendpoints"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

module "vmtest" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-vm?ref=v1.4.0"
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  resource_group_name  = module.rg.rgrp.name
  function             = "app"
  index                = local.index
  size                 = "Standard_D2s_v4"
  admin_username       = "azureadmin"
  key_vault_id         = data.azurerm_key_vault.kv.id
  admin_secret_name    = module.secret.azurerm_key_vault_secret.name 
  subnet_id_for_nics   = module.subnet.snet.id
  zone                 = "" //Keep empty string ("") if you don't want to specify the Availabilty Zone, otherwise please enter the zone ("1" || "2" || "3" || etc)
  license_type         = "Windows_Server" //Make sure this suits the image you are using. Possible values are None, Windows_Client and Windows_Server.
  source_image_reference = {
    publisher = "MicrosoftWindowsServer"
    offer     = "WindowsServer"
    sku       = "2022-datacenter-g2"
    version   = "latest"
  }

  builtin_metric_monitoring   = false
  resource_health_monitoring  = false  

  depends_on = [ module.secret ]
}

# az vm extension image list --location westeurope

resource "azurerm_virtual_machine_extension" "az_network_watcher" {
  name                       = "AzureNetworkWatcherExtension"
  virtual_machine_id         = module.vmtest.vm.id
  publisher                  = "Microsoft.Azure.NetworkWatcher"
  type_handler_version       = "1.4"
  type                       = "NetworkWatcherAgentWindows"
  auto_upgrade_minor_version = true
  automatic_upgrade_enabled  = false
}

output "vm" {
  value       = module.vmtest.vm
  description = "Windows VM object"
  sensitive   = true
}