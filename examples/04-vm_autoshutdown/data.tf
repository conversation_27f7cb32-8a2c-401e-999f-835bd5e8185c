# Subnet for vnic - assuming a subnet has already been created
data "azurerm_subnet" "vm_subnet" {
  name                 = var.snet_name
  virtual_network_name = var.vnet_name
  resource_group_name  = var.vnet_rgrp_name
}

# Key vault for storing a secret
data "azurerm_key_vault" "kv" {
  name                = var.kv_name
  resource_group_name = var.kv_rgrp_name
}

# Required for using image gallery image
data "azurerm_shared_image" "shared_image" {
  name                = module.conventions.azure_images.windows.name
  gallery_name        = module.conventions.azure_images.windows.gallery_name
  resource_group_name = module.conventions.azure_images.windows.resource_group_name
  provider            = azurerm.image_source #it must be defined in provider.tf
}