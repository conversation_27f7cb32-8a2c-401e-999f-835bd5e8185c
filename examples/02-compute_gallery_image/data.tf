# Subnet for vnic - assuming a subnet has already been created
data "azurerm_subnet" "vm_subnet" {
  name                 = var.snet_name
  virtual_network_name = var.vnet_name
  resource_group_name  = var.vnet_rgrp_name
}

# Key vault for storing a secret
data "azurerm_key_vault" "kv" {
  name                = var.kv_name
  resource_group_name = var.kv_rgrp_name
}

# Required for using image gallery image you can use your own image gallery
data "azurerm_shared_image" "shared_image" {
  name                = "otp_windows_2019_gold"       #the version of the image
  gallery_name        = "cogaweuprdwindows"            #the name of the image gallery
  resource_group_name = "rgrp-weu-prd-compute_gallery" #the name of the resource group the the image gallery exists
  provider            = azurerm.image_source           #it must be defined in provider.tf
}