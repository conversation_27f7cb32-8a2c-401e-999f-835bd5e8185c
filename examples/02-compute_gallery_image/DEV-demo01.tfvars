#Conventions
cloud       = "azure"
environment = "dev"
region      = "westeurope"
project     = "abb"

//Vnet
vnet_name = "vnet-weu-dev-demo01-01"
vnet_rgrp_name = "rgrp-weu-dev-demo01-01"
snet_name      = "snet-test1"

//Route table
rtbl_name       = "rtbl-weu-dev-demo01-core"
rtbl_rgrp_name  = "rgrp-weu-dev-demo01-01"

//Key Vault
kv_name = "kvau-weu-dev-DEVD000001"
kv_rgrp_name = "rgrp-weu-dev-demo01-01"
