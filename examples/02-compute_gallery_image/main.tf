locals {
  project       = "wvm"
  index         = "20"
  suffix        = "${local.project}-${local.index}"
  resource_name = "${module.conventions.short_region}-${module.conventions.environment}-${local.suffix}"
}

// Generate admin password and add it to key vault
module "password" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//password?ref=v2.1.1"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "secret" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-secret?ref=v1.5.0"
  conventions     = module.conventions
  key_vault_id    = data.azurerm_key_vault.kv.id
  name            = local.resource_name
  value           = module.password.password
  expiration_date = time_offset.expirationdate.rfc3339
}

module "rg" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "vmtest" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-vm?ref=v3.1.1"
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  resource_group_name  = module.rg.rgrp.name
  function             = "app"
  index                = local.index
  size                 = "Standard_D2s_v4"
  subnet_id_for_nics   = data.azurerm_subnet.vm_subnet.id //Using an existing subnet
  admin_username       = "azureadmin"
  key_vault_id         = data.azurerm_key_vault.kv.id
  admin_secret_name    = module.secret.azurerm_key_vault_secret.name   
  zone                 = ""                                                //Keep empty string ("") if you don't want to specify the Availabilty Zone, otherwise please enter the zone ("1" || "2" || "3" || etc)
  source_image_id      = data.azurerm_shared_image.shared_image.id #referring to gallery image id, which is defined in data block
  license_type         = "Windows_Server"                                  //Make sure this suits the image you are using. Possible values are None, Windows_Client and Windows_Server.  

  builtin_metric_monitoring   = false
  resource_health_monitoring  = false

  az_network_watcher_installation = false

  auto_shutdown = {
    daily_recurrence_time = "1900"
    // all other parameters are optional
    notification_enabled = true
    notification_email   = "<EMAIL>"
  }

  depends_on = [ module.secret ]
}

