data "azurerm_subnet" "privateendpoint" {
  name                 = var.snet_name
  virtual_network_name = var.vnet_name
  resource_group_name  = var.vnet_rgrp_name
}

data "azurerm_client_config" "current" {}

data "azurerm_virtual_network" "vnet" {
  name                = var.vnet_name
  resource_group_name = var.vnet_rgrp_name
}

data "azurerm_route_table" "default_rtbl" {
  name                = var.rtbl_name
  resource_group_name = var.vnet_rgrp_name
}

