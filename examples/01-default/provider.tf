#provider - All provider configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    key_vault {
      purge_soft_delete_on_destroy               = true
      recover_soft_deleted_key_vaults            = true
      purge_soft_deleted_certificates_on_destroy = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.41.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.13.1"
    }
  }
}

provider "azurerm" {
  features {}
  alias           = "logging"
  subscription_id = "a3a538d4-3d2b-4b05-b268-f147b8788b86"
}
