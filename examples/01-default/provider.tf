#provider - All provider configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true  
}

provider "azapi" {
  use_oidc = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    azapi = {
      source  = "Azure/azapi"
      version = "2.1.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.9.1"
    }
  }
}
