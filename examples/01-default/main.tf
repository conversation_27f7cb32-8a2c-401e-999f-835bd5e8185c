
locals {
  project       = "wvm"
  index         = "19"
  suffix        = "${local.project}-${local.index}"
  resource_name = "${module.conventions.short_region}-${module.conventions.environment}-${local.suffix}"
}

// Generate admin password and add it to key vault
module "password" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//password?ref=v2.1.1"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "secret" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-secret?ref=v1.5.0"
  conventions     = module.conventions
  key_vault_id    = data.azurerm_key_vault.kv.id
  name            = local.resource_name
  value           = module.password.password
  expiration_date = time_offset.expirationdate.rfc3339
}

module "rg" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "managed_disk_module_1" {
  //OPTIONAL MODULE! If you don't need it you can skip the managed_disk_module1 2 ...
  //Checkov
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk//manageddisk?ref=v1.3.0"
  conventions           = module.conventions
  resource_group_name   = module.rg.rgrp.name
  resource_name_suffix  = "${local.project}-01"
  disk_size_gb          = "16"
  network_access_policy = "DenyAll"
}

module "managed_disk_module_2" {
  //OPTIONAL MODULE! If you don't need it you can skip the managed_disk_module1 2 ...
  //Checkov
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-managed-disk//manageddisk?ref=v1.3.0"
  conventions           = module.conventions
  resource_group_name   = module.rg.rgrp.name
  resource_name_suffix  = "${local.project}-02"
  disk_size_gb          = "16"
  network_access_policy = "DenyAll"
}

module "vmtest" {
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-vm?ref=v3.1.1"
  source               = "../.."
  conventions          = module.conventions
  resource_name_suffix = local.suffix
  resource_group_name  = module.rg.rgrp.name
  function             = "app"
  index                = local.index
  size                 = "Standard_D2s_v4"
  subnet_id_for_nics   = data.azurerm_subnet.vm_subnet.id //Using an existing subnet
  admin_username       = "azureadmin"
  key_vault_id         = data.azurerm_key_vault.kv.id //Using an existing key vault where the secret is stored
  admin_secret_name    = module.secret.azurerm_key_vault_secret.name 
  zone                 = ""                                                //Keep empty string ("") if you don't want to specify the Availabilty Zone, otherwise please enter the zone ("1" || "2" || "3" || etc)
  source_image_id      = data.azurerm_shared_image.shared_image.id #referring to gallery image id, which is defined in data block
  license_type         = "Windows_Server" //Make sure this suits the image you are using. Possible values are None, Windows_Client and Windows_Server.

  az_network_watcher_installation = false

  //In case you need to swap the OS disk outside of Terraform, use this parameter to refresh Terraform configuration:
  //os_disk_name_override = "odsk-weu-tst-wvm-191"

  //builtin_metric_monitoring  = false
  //resource_health_monitoring = false
  
  // Optional block to configure eventlog collection to log analytics workspace
  // If log_analytics_workspace_id is not defined data is sent to shared log analytics workspace

  eventlog_settings = [
    {
      name = "dcr-data-source-eventlogs"
      streams = ["Microsoft-Event"]
      x_path_queries = ["Application!*[System[(Level=1 or Level=2 or Level=3)]]","System!*[System[(Level=1 or Level=2 or Level=3)]]"]
    }
  ]

  // Optional parameter to send additional performance metrics to log analytics workspace
  perf_counters = [
    {
      name                          = "perfmetrics"
      counter_specifiers            = ["\\Memory\\Available Bytes","\\System\\Processes"]
      sampling_frequency_in_seconds = 120
    }
  ]

  //Optional block to create auto-shutdown schedule
  /*
  auto_shutdown = {
    daily_recurrence_time = "1900"
  }
  */
 
  //Following code block can be omitted
  //Start of omittable block
  //It is not mandatory to add these variables into your blueprint, the following lines describes the situation when you want to attach one or more attached disks to the VM
   attached_disk_vars = [{
    attachable_managed_disk_id    = module.managed_disk_module_1.mdsk.id,
    data_disk_logical_unit_number = 0,     //Must be unique value among the disks!
    attached_data_disk_caching    = "None" // Possible values are None, ReadOnly and ReadWrite
    },
    {
      attachable_managed_disk_id    = module.managed_disk_module_2.mdsk.id,
      data_disk_logical_unit_number = 1,          //Must be unique value among the disks!
      attached_data_disk_caching    = "ReadWrite" // Possible values are None, ReadOnly and ReadWrite
  }]
  //End of omittable block

  depends_on = [ module.secret ]
}

