locals {
  resource_name = "akstest0515"
}

module "k8s" {
  source                = "../.."
  //source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  resource_name_suffix  = local.resource_name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  #ingress_type          = "nginx"
  #ingress_nginx_ip      = var.ingress_nginx_ip
  prometheus_enable     = true
  temporary_name_for_rotation = "temptestname"
  only_critical_addons_enabled  = true

  sys_pool_node_size      = "Standard_D4ds_v5"
  sys_pool_node_count_max = 3
  sys_pool_node_labels = {
    name = "testlabel01"
  }

  // Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "0321"
/*
  // Default logging is disable from v1.4.4. You need to configure logging explicitely like below. Default workspace is the shared log analytics workspace.
  loganalytics = {
    defender = ""
    oms      = ""
    diag = [{
      log = ["kube-apiserver",
      "kube-audit",
      "kube-controller-manager",
      "kube-scheduler",
      "cluster-autoscaler",
      "kube-audit-admin",
      "guard",
      "cloud-controller-manager",
      "csi-azuredisk-controller",
      "csi-azurefile-controller",
      "csi-snapshot-controller"]
      metric = []
      workspace_id = module.conventions.log_analytics_workspace_id
    }]

  }
*/ 

  // Use below block if logging is not required
  loganalytics = {
    defender = null
    oms      = module.conventions.log_analytics_workspace_id
    diag = []
  }

  # Configure container insights
  container_insights_enable = true
  container_insights_configmap_filename = "ama-logs-rs-config_v2.yaml"

  // Builtin monitoring can be disabled here
  builtin_metric_monitoring   = false
  resource_health_monitoring  = false

  // Enable workload identity
  // aks_workload_identity_enabled = true

  // Create a dedicated user node pool with minimal set of parameters - see example 13 for complete example
  aks_nodepool = [
    {
      name                 = "testpool1"
      nodepool_subnet_id   = data.azurerm_subnet.snet-aks.id
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size  = "Standard_D4ds_v5"
      max_pods = 50
    }
  ]

}


