locals {
  index  = format("%02d", random_integer.index.result)
  kind   = "aifoun"
  suffix = "${local.kind}${local.index}"
}

resource "random_integer" "index" {
  min = 10
  max = 99
}


# Resource Group
module "rg01" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.1"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

resource "azurerm_user_assigned_identity" "foundry" {
  location            = module.conventions.region
  name                = "umid-weu-${module.conventions.environment}-foundry-${local.suffix}"
  resource_group_name = module.rg01.rgrp.name
}

resource "azurerm_role_assignment" "umid_kv_enc" {
  scope                = data.azurerm_key_vault.default.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.foundry.principal_id
}
resource "time_sleep" "role_assignment_wait" {
  depends_on      = [azurerm_role_assignment.umid_kv_enc]
  create_duration = "120s"
}

resource "time_offset" "expirationdate" {
  offset_days = 170
}

module "aifoundrykey" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.8.1"
  conventions     = module.conventions
  key_name        = "testkey0${local.suffix}"
  key_vault_id    = data.azurerm_key_vault.default.id
  key_type        = "RSA"
  key_size        = 4096
  key_opts        = ["encrypt", "wrapKey", "unwrapKey", "decrypt", "sign"]
  expiration_date = time_offset.expirationdate.rfc3339
}


##### END OF PREREQUISITES
###########################

module "aifoundry01" {
  source = "../.."
  //source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-ai-foundry?ref=v2.0.0"
  resource_name_suffix = local.suffix
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id

  identity_ids = [azurerm_user_assigned_identity.foundry.id]
  customer_managed_key = {
    identity_client_id = azurerm_user_assigned_identity.foundry.client_id
    key_vault_key_id   = module.aifoundrykey.azurerm_key_vault_key.id
  }

  log_analytics_diag_logs = ["AllLogs"]
  log_analytics_metrics   = ["AllMetrics"]
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id

  aifoundry_sku                = "S0"
  aifoundry_custom_domain_name = var.environment == "prd" ? "otpprd${local.suffix}" : "otpnonprod${local.suffix}"
  depends_on                   = [module.rg01, time_sleep.role_assignment_wait]
}
