locals {
  suffix = "winapp01"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}


//Dinamic subnet creation is used for testing, not recommended in production!
module "subnet_tools" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/tooling//snet-tools?ref=v4.3.1"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  subnet_prefixes_length = [28]
}

module "delegated_subnet" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.4.0"
  conventions            = module.conventions
  resource_name_suffix   = "${local.suffix}-dlg"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table = data.azurerm_route_table.default_rtbl.id
  delegation = [{
    name = "managedinstancedelegation"
    service_delegation = {
      name    = "Microsoft.Web/serverFarms"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
    }
  }]
  address_prefix = module.subnet_tools.next_subnets[0]
}

// Optionally one or more storage account can be connected to webapp - azapi provider is required for storageaccount >= v1.2.0 
module "sa" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  #checkov:skip=CKV2_AZURE_21: Logging is executed via security policies
  #checkov:skip=CKV_AZURE_34: Default value is private.
  source                        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount?ref=v3.0.0"
  conventions                   = module.conventions
  resource_group_name           = module.rg01.rgrp.name
  resource_name_suffix          = local.suffix
  subnet_id                     = data.azurerm_subnet.privateendpoint.id
  wait_after                    = 120
  public_network_access_enabled = false
  shared_access_key_enabled     = true    // The Files & Table Storage API's do not support authenticating via AzureAD and will continue to use a SharedKey to access the API's.
  account_tier                  = "Premium"
  account_kind                  = "FileStorage"
  subresource_list              = ["file"]
  account_replication_type      = "ZRS"
  large_file_share_enabled      = true

  //For this test monitoring can be disabled
  resource_health_monitoring = false
  builtin_metric_monitoring  = false

  stac_share = {
    share1 = {
      name                      = "webapp-share01"
      quota_gb                  = 100
      enabled_protocol          = "SMB"
      container_access_type     = "private"
    }
  }

}

module "mywindowswebapp" {
  # source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-web-app?ref=v4.0.1"
  source                        = "../.."
  conventions                   = module.conventions
  resource_group_name           = module.rg01.rgrp.name
  resource_name_suffix          = "01"
  subnet_id                     = data.azurerm_subnet.privateendpoint.id
  delegated_subnet_id           = module.delegated_subnet.snet.id
  azurerm_service_plan_sku_name = "P1v3"
  worker_count                  = 2

  site_config = {
    always_on              = true
    health_check_path      = "/"
    health_check_eviction_time_in_min = 2
    application_stack = {
      current_stack  = "dotnet"
      dotnet_version = "v7.0"
    }
  }

  // Optional storage account connection
  storage_accounts = [
    {
      stac_object  = module.sa.stac
      share_name   = "webapp-share01"
      type         = "AzureFiles"
      mount_path   = "\\mounts\\webapp"
    }
  ]

  app_settings = {
    "WEBSITE_DNS_SERVER" : "10.1.65.52" //This is the default value, any other value will be overwritten.
    #"WEBSITE_RUN_FROM_PACKAGE" : 1
  }

  auth_settings = {
    enabled = true
    active_directory = {
      client_id = data.azurerm_client_config.current.client_id
    }
  }

  // Log analytics parameters are optional below
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["AppServiceAntivirusScanAuditLogs","AppServiceAppLogs","AppServiceAuditLogs","AppServiceConsoleLogs","AppServiceFileAuditLogs","AppServiceHTTPLogs","AppServiceIPSecAuditLogs","AppServicePlatformLogs","FunctionAppLogs","WorkflowRuntime"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]
}

