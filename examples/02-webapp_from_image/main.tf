locals {
  suffix = "winapp02"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.suffix
}

module "acr" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.5.0"
  conventions           = module.conventions
  resource_name_suffix  = local.suffix
  resource_group_name   = module.rg01.rgrp.name
  subnet_id             = data.azurerm_subnet.privateendpoint.id
  generate_admin_token  = false
  enable_quarantine     = false

  builtin_metric_monitoring  = false
  resource_health_monitoring = false

  identity = {
    type = "SystemAssigned"
  }
}

resource "time_sleep" "wait-private-dns" {
  depends_on = [module.acr]
  create_duration = "500s"
}

module "nexus2acr" {
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-tools-nexus2acr?ref=v3.0.0"
  depends_on  = [time_sleep.wait-private-dns]
  acr_name    = module.acr.acre.name

  source_images = [
    "anonymous-proxy-do-quay-io.otpnexus.hu/prometheus/prometheus:v2.42.0"
  ]
}

//Dinamic subnet creation is used for testing, not recommended in production!
module "subnet_tools" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/tooling//snet-tools?ref=v4.3.1"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  subnet_prefixes_length = [28]
}

module "delegated_subnet" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash    
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.4.0"
  conventions            = module.conventions
  resource_name_suffix   = "${local.suffix}-dlg"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table = data.azurerm_route_table.default_rtbl.id
  delegation = [{
    name = "managedinstancedelegation"
    service_delegation = {
      name    = "Microsoft.Web/serverFarms"
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
    }
  }]
  address_prefix = module.subnet_tools.next_subnets[0]
}

module "mywindowswebapp" {
  # source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-windows-web-app?ref=v4.0.1"
  source                        = "../.."
  conventions                   = module.conventions
  resource_group_name           = module.rg01.rgrp.name
  resource_name_suffix          = "01"
  subnet_id                     = data.azurerm_subnet.privateendpoint.id
  delegated_subnet_id           = module.delegated_subnet.snet.id
  azurerm_service_plan_sku_name = "P1v3"
  worker_count                  = 2

  site_config = {
    always_on              = true
    health_check_path      = "/"
    health_check_eviction_time_in_min = 2
    
    container_registry_managed_identity_client_id = module.acr.acre.identity[0].principal_id
    container_registry_use_managed_identity       = true

    application_stack = {
      docker_image_name   = "prometheus/prometheus:v2.42.0"
      docker_registry_url = "https://acreweudeviacnexus2acr.azurecr.io"
    }
  }

  app_settings = {
    "WEBSITE_DNS_SERVER" : "**********" //This is the default value, any other value will be overwritten.
    #"WEBSITE_RUN_FROM_PACKAGE" : 1
  }

  auth_settings = {
    enabled = true
    active_directory = {
      client_id = data.azurerm_client_config.current.client_id
    }
  }

  depends_on = [ module.nexus2acr ]
}

