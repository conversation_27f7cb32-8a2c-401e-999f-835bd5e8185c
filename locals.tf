locals {
  base_name     = lower("${var.conventions.project}${var.function}${var.index}w${var.conventions.short_environment}a${var.conventions.short_region}")
  resource_name = "${var.conventions.short_region}-${var.conventions.environment}-${var.resource_name_suffix}"
  vm_name       = local.base_name
  os_disk_name  = "odsk-${local.resource_name}"

  ################################ Tags
  tags = merge(
    var.conventions.tags,
    var.winvm_tags,
    {
      EnablePrivateNetworkGC       = var.guest_config_enabled ? "TRUE" : "FALSE"
      creation_mode                = "terraform",
      terraform-azurerm-windows-vm = "v3.1.1",
      domain                       = "${var.domain_name}"
      sccm                         = "${var.sccm_group}"
    }
  )

}
