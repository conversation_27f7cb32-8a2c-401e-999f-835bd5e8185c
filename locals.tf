locals {
  //Tags
  tags = merge(
    var.conventions.tags,
    var.windows_web_app_tags,
    {
      creation_mode                       = "terraform",
      terraform-azurerm-windows-web-app   = "v4.0.1",
    }
  )

  //Names
  resource_shortname_aasp = "aasp"
  service_plan_name       = "${local.resource_shortname_aasp}-${var.conventions.short_region}-${var.conventions.environment}-${var.conventions.project}-${var.resource_name_suffix}"
  resource_shortname_asvw = "asvw"
  windows_web_app_name    = replace(local.service_plan_name, "aasp", "asvw")
}

