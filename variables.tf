variable "conventions" {
  type        = any
  description = "(Required) terraform-conventions module"

  validation {
    condition     = can(regex("^[a-z0-9]{2,}$", var.conventions.project))
    error_message = "Only alphanumeric characters are allowed and should contain at least two characters."
  }
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix"

  validation {
    condition     = can(regex("^[a-z0-9]{2,}$", var.resource_name_suffix))
    error_message = "Only lower case alphanumeric characters are allowed and should contain at least two characters"
  }
}

variable "resource_group_name" {
  type        = string
  description = "(Required) The name of the resource group in which to create the storage account."

  validation {
    condition     = length(var.resource_group_name) > 0
    error_message = "The resource group name must not be empty."
  }
}

variable "customer_managed_key" {
  type = object({
    key_vault_key_id   = string
    identity_client_id = string
  })
  description = <<EOT
  (Required) A customer_managed_key block supports the following:
    key_vault_key_id - (Required) The ID of the Key Vault Key which should be used to Encrypt the data in this Cognitive Account.
    user_assigned_identity_id - (Required) The Client ID of the User Assigned Identity that has access to the key. 
  EOT
}

variable "acr_id" {
  type        = string
  description = "(Optional) ID of the container registry associated with this workspace."
  default     = null
}

variable "subnet_id" {
  type        = string
  description = "(Required) ID of the subnet where private endpoint should be created."
}

variable "identity_ids" {
  type        = list(string)
  description = "(Required) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Cognitive Service. A SystemAssigned identity will always be created."
}

variable "ai-foundry_tags" {
  description = "(Optional) Tags to apply to AI foundry service"
  type        = map(string)
  default     = null
}

variable "aifoundry_sku" {
  type        = string
  default     = "S0"
  description = "(Required) The name of the SKU. Ex - P3. It is typically a letter+number code."
}

variable "outbound_network_access_restricted" {
  type        = bool
  default     = true
  description = "(Optional) Whether outbound network access is restricted for the AI Services Account. Defaults to true."
}

variable "public_network_access_enabled" {
  type        = bool
  default     = false
  description = "(Optional) Whether public network access is allowed for the AI Services Account. Possible values are true and false. Defaults to false."
}

variable "network_acl_bypass" {
  type        = string
  default     = "None"
  description = "(Optional) Specifies which traffic can bypass the network rules. Possible values are AzureServices and None."
}

variable "virtual_network_rules" {
  type = list(object({
    subnet_id                            = string
    ignore_missing_vnet_service_endpoint = bool
  }))
  default     = []
  description = <<EOT
   (Optional) List of virtual network rules to apply to the AI Services Account.
   subnet_id - (Required) The ID of the subnet which should be able to access this AI Services Account.
   ignore_missing_vnet_service_endpoint - (Optional) Whether to ignore a missing Virtual Network Service Endpoint or not. Default to false.
   EOT
}

variable "aifoundry_custom_domain_name" {
  type        = string
  default     = null
  description = "(Optional)	Optional subdomain name used for token-based authentication."
}

variable "network_isolation_mode" {
  type        = string
  default     = "AllowOnlyApprovedOutbound"
  description = "(Optional) The isolation mode of the Machine Learning Workspace. Possible values are Disabled, AllowOnlyApprovedOutbound, and AllowInternetOutbound"

  validation {
    condition     = contains(["Disabled", "AllowOnlyApprovedOutbound", "AllowInternetOutbound"], var.network_isolation_mode)
    error_message = "Provide authorized value: Disabled, AllowOnlyApprovedOutbound, AllowInternetOutbound."
  }
}


//Diagnostic settings variables
variable "log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace"
  type        = string
  default     = null
}

variable "log_analytics_diag_logs" {
  description = "(Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "log_analytics_metrics" {
  description = "(Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}

//Monitoring and alerts
variable "resource_health_monitoring" {
  description = "(Optional) Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null
}

variable "builtin_metric_monitoring" {
  description = "(Optional) Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "alert_SuccessRate_threshold" {
  description = "(Optional) Threshold for Availability Rate alert rule."
  type        = number
  default     = 90
}

