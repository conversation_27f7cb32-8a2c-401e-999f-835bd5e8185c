variable "conventions" {
  description = "(Required) Custom parameter modul"
  validation {
    condition     = length(var.conventions.project) == 3
    error_message = "must be 3 character long"
  }
}

variable "winvm_tags" {
  description = "Tags to applied to Windows VM"
  type        = map(string)
  default     = null
}

variable "guest_config_enabled" {
  description = "(Optional) This parameter must be set to true if guest configuration policies are applied to the machine. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix"
}

variable "resource_group_name" {
  type        = string
  description = "(Required) The name of the Resource Group in which the Windows Virtual Machine should be exist. Changing this forces a new resource to be created."
}

variable "size" {
  type        = string
  description = " (Required) The SKU which should be used for this Virtual Machine, such as Standard_F2."
}

variable "function" {
  type        = string
  description = "(Required) Part of the custom naming"
  validation {
    condition     = length(var.function) == 3
    error_message = "must be 3 character long"
  }
}

variable "index" {
  type        = string
  description = "(Required) Part of the custom naming"
  validation {
    condition     = length(var.index) == 2
    error_message = "must be 2 character long"
  }
}

variable "zone" {
  type        = string
  description = "(Optional) Specifies the Availability Zone in which this Windows Virtual Machine should be located. Changing this forces a new Windows Virtual Machine to be created. Keep empty string if you don't want to specify the Availabilty Zone, otherwise please enter the zone"
  default     = ""
}

variable "admin_username" {
  type        = string
  description = "(Required) The username of the local administrator used for the Virtual Machine. Changing this forces a new resource to be created."
}

variable "source_image_id" {
  type        = string
  default     = null
  description = "(Optional) The ID of the Image which this Virtual Machine should be created from. Changing this forces a new resource to be created. Possible Image ID types include Image IDs, Shared Image IDs, Shared Image Version IDs, Community Gallery Image IDs, Community Gallery Image Version IDs, Shared Gallery Image IDs and Shared Gallery Image Version IDs."
}

variable "source_image_reference" {
  type = object({
    publisher = string
    offer     = string
    sku       = string
    version   = string
  })
  default     = null
  description = "(Optional) A source_image_reference block as defined below. Changing this forces a new resource to be created."
}

variable "compute_gallery_image" {
  type = object({
    version             = string
    name                = string
    gallery_name        = string
    resource_group_name = string
    subscription_id     = string
  })
  default     = null
  description = "(Optional A compute_gallery_image block as defined in examples.)"
}

variable "subnet_id_for_nics" {
  type        = string
  description = "(Required) the ID of the subnet for the Network Interface (vnic) "
  default     = ""
}

variable "boot_diagnostics_storage_account_uri" {
  type        = string
  description = "(Optional) The Primary/Secondary Endpoint for the Azure Storage Account which should be used to store Boot Diagnostics, including Console Output and Screenshots from the Hypervisor. Passing a null value will utilize a Managed Storage Account to store Boot Diagnostics."
  default     = null
}

variable "storage_account_type" {
  type        = string
  description = "(Required) The Type of Storage Account which should back this the Internal OS Disk. Defaults to Premium_LRS."
  default     = "Premium_LRS"
  validation {
    condition     = contains(["Standard_LRS", "StandardSSD_LRS", "Premium_LRS", "StandardSSD_ZRS", "Premium_ZRS"], var.storage_account_type)
    error_message = "Provide authorized value: Standard_LRS, StandardSSD_LRS, Premium_LRS, Premium_ZRS, StandardSSD_ZRS"
  }
}
variable "license_type" {
  type        = string
  description = "(Optional) Specifies the type of on-premise license (also known as Azure Hybrid Use Benefit) which should be used for this Virtual Machine. Possible values are None, Windows_Client and Windows_Server."
  default     = "Windows_Server"
}

variable "builtin_metric_monitoring" {
  description = "Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "resource_health_monitoring" {
  description = "Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null  
}

variable "alert_percentage_CPU_threshold" {
  type        = number
  description = "Threshold for Percentage CPU alert rule."
  default     = 90
}

variable "alert_Data_Disk_Bandwidth_Consumed_Percentage_threshold" {
  type        = number
  description = "Threshold for Data Disk Bandwidth Consumed Percentage alert rule."
  default     = 90
}

variable "alert_Data_Disk_IOPS_Consumed_Percentage_threshold" {
  type        = number
  description = "Threshold for Data Disk IOPS Consumed Percentage alert rule."
  default     = 90
}

variable "alert_OS_Disk_Bandwidth_Consumed_Percentage_threshold" {
  type        = number
  description = "Threshold for OS Disk Bandwidth Consumed Percentage alert rule."
  default     = 90
}

variable "alert_OS_Disk_IOPS_Consumed_Percentage_threshold" {
  type        = number
  description = "Threshold for OS Disk IOPS Consumed Percentage alert rule"
  default     = 90
}


variable "alert_Available_Memory_Bytes_threshold" {
  type        = number
  description = "Threshold for Available Memory Bytes alert rule"
  default     = 1000

}

variable "attached_disk_vars" {
  type = list(object({
    attachable_managed_disk_id    = optional(string)
    data_disk_logical_unit_number = optional(number)
    attached_data_disk_caching    = optional(string)
  }))

  default = []

  description = <<-EOT
  The attached_disk_vars variable has 3 parameters. Default value is []. If you want to attach one or more disks you must add value to all 3 variables:
   attachable_managed_disk_id - (Required) The ID of the attached disk which you want to attach. The variable attached_disk_vars must be set as in example 01, then the module creates as many managed disks as many necessary (including 0) and attaches it to the current VM
   data_disk_logical_unit_number - (Required) The Logical Unit Number of the Data Disk, which MUST be unique within the Virtual Machine. Changing this forces a new resource to be created. Default value is 0
   attached_data_disk_caching - (Required) Specifies the caching requirements for this Data Disk. Possible values include None, ReadOnly and ReadWrite. Default value is None
  EOT
}

variable "log_analytics_workspace_id" {
  description = "Optional. If eventlog_settings is defined and this variable is not defined logs will be sent to shared log analytics workspace. Define this variable if you need different destination workspace."
  type        = string
  default     = null
}

variable "eventlog_settings" {
  type = list(object({
    name           = string
    streams        = list(string)
    x_path_queries = list(string)
  }))
  description = <<-EOT
    eventlog_settings variable need to be defined if logging is required.
    Define one or more eventlog logging configuration with following parameters:
      name - (Required) The name which should be used for this data source. This name should be unique across all data sources regardless of type within the Data Collection Rule.
      streams - (Required) Specifies a list of streams that this data source will be sent to. A stream indicates what schema will be used for this data and usually what table in Log Analytics the data will be sent to. Possible values include but not limited to Microsoft-Event,and Microsoft-WindowsEvent, Microsoft-RomeDetectionEvent, and Microsoft-SecurityEvent.
      x_path_queries - (Required) Specifies a list of Windows Event Log queries in XPath expression. Please see this document for more information.
  EOT
  default     = null
}

variable "perf_counters" {
  type = list(object({
    counter_specifiers            = list(string)
    name                          = string
    sampling_frequency_in_seconds = number
  }))
  description = <<-EOT
    Collect additional performance counters by using this variable. Available metrics: https://learn.microsoft.com/en-us/azure/azure-monitor/agents/data-sources-performance-counters
    (Optional) One or more perf_counter blocks as defined below.
      name - (Required) The name which should be used for this data source. This name should be unique across all data sources regardless of type within the Data Collection Rule.
      counter_specifiers - (Required) Specifies a list of specifier names of the performance counters you want to collect. To get a list of performance counters on Windows, run the command typeperf. Please see this document for more information.      
      sampling_frequency_in_seconds - (Required) The number of seconds between consecutive counter measurements (samples). The value should be integer between 1 and 300 inclusive. sampling_frequency_in_seconds must be equal to 60 seconds for counters collected with Microsoft-InsightsMetrics stream.
  EOT
  default     = null
}

variable "ultra_ssd_enabled" {
  type        = bool
  description = "(Optional) Should the capacity to enable Data Disks of the UltraSSD_LRS storage account type be supported on this Virtual Machine? Defaults to false."
  default     = false
}

variable "ama_installation" {
  type        = bool
  description = "(Optional) Azure Monitor Agent must be running on all VMs. Set this parameter to false only if you want to let Azure policies to install the agent."
  default     = true
}

variable "ama_auto_minor_upgrade" {
  type        = bool
  description = "(Optional) Specifies if the platform deploys the latest minor version update to the type_handler_version specified. Defaults to true."
  default     = true
}

variable "ama_auto_upgrade" {
  type        = bool
  description = "(Optional) Should the Extension be automatically updated whenever the Publisher releases a new version of Azure Monitor Agent. Defaults to false."
  default     = false
}

variable "az_network_watcher_installation" {
  type        = bool
  description = "(Optional) AZ Network Watcher can be installed on VM. defaults to false."
  default     = false
}

variable "az_network_watcher_auto_minor_upgrage" {
  type        = bool
  description = "(Optional) Specifies if the platform deploys the latest minor version update to the type_handler_version specified. Defaults to true."
  default     = true
}

variable "az_network_watcher_auto_upgrade" {
  type        = bool
  description = "(Optional) Should the Extension be automatically updated whenever the Publisher releases a new version of AZ Network Watcher. Defaults to false."
  default     = false
}

variable "dce_association" {
  type        = bool
  description = "(Optional) VMs must be associated with DCEs to send logs through AMPLS. Set this parameter to false only if you want to let Azure policies to perform DCE association."
  default     = true
}

variable "appgw_backend_address_pool_id" {
  type        = string
  default     = null
  description = "(Optional) Use this variable only if you want to use the VM as an Application Gateway backend. The ID of the Application Gateway's Backend Address Pool which this Network Interface which should be connected to. Changing this forces a new resource to be created."
}

variable "appgw_vnic_ip_configuration_name" {
  type        = string
  default     = "internal"
  description = "(Optional) The Name of the IP Configuration within the Network Interface which should be connected to the Backend Address Pool. Changing this forces a new resource to be created. Defaults to internal which is set by vnic module."
}

variable "domain_name" {
  type        = string
  default     = "corp.otpbank.hu"
  description = "(Optional) The name of the AD domain where the VM is goint to join."
}

variable "sccm_group" {
  type        = string
  default     = "sccm-every-sun-08-18"
  description = "(Optional) The name of SCCM group that will take care of VM patching."

  validation {
    condition     = contains(["sccm-daily-14-16", "sccm-daily-18-21", "sccm-every-sun-03-05", "sccm-every-sun-08-18", "sccm-2nd-3rd-sun-10-14", "sccm-every-mon-03-06", "sccm-every-mon-thu-05-06", "sccm-2nd-3rd-mon-20-23", "sccm-3rd-4th-sun-20-24", "sccm-3rd-4th-mon-22-23-40"], var.sccm_group)
    error_message = "Provide valid SCCM group: sccm-daily-14-16, sccm-daily-18-21, sccm-every-sun-03-05, sccm-every-sun-08-18, sccm-2nd-3rd-sun-10-14, sccm-every-mon-03-06, sccm-every-mon-thu-05-06, sccm-2nd-3rd-mon-20-23, sccm-3rd-4th-sun-20-24, sccm-3rd-4th-mon-22-23-40"
  }
}

variable "identity_type" {
  type        = string
  default     = "SystemAssigned"
  description = "(Optional) Specifies the type of Managed Service Identity that should be configured on this Windows Virtual Machine. Possible values are SystemAssigned (default), UserAssigned, SystemAssigned, UserAssigned (to enable both)."
}

variable "identity_ids" {
  type        = list(string)
  default     = null
  description = "(Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Windows Virtual Machine."
}

variable "disk_encryption_set_id" {
  type        = string
  description = "(Optional) The ID of the Disk Encryption Set which should be used to Encrypt this OS Disk."
  default     = ""
}

variable "os_disk_caching" {
  type        = string
  description = "(Optional) Specifies the caching requirements for the Data Disk. Possible values include None, ReadOnly and ReadWrite. Defaults to ReadWrite"
  default     = "ReadWrite"
}

variable "os_disk_name_override" {
  type        = string
  description = "(Optional) Override the OS disk name. This parameter need to be used when OS disk was swapped outside of Terraform. azapi_update_resource will be still required and executed at the next apply."
  default     = null
}

variable "vmname_override" {
  type        = string
  description = "(Optional) Override the Virtual Machine resource name"
  default     = null
}

variable "computer_name_override" {
  type        = string
  description = "(Optional) Override the operating system computer name"
  default     = null
}

variable "key_vault_id" {
  type        = string
  description = "(Required) The ID of the Key Vault where the admin password is stored."
}

variable "admin_secret_name" {
  type        = string
  description = "(Required) Name of the Key Vault Secret, where the administrator password is stored."
}

variable "auto_shutdown" {
  type = object({
    enabled                   = optional(bool,true)
    timezone                  = optional(string,"Central Europe Standard Time")
    daily_recurrence_time     = string
    notification_enabled      = optional(bool,false)
    notification_time_in_mins = optional(number,30)
    notification_webhook_url  = optional(string,null)
    notification_email        = optional(string,null)
  })
  default     = null
  description = <<EOT
    (Optional) Manages an automated shutdown schedule for the VM. If this variable is not used auto-shutdown schedule is NOT configured.
      daily_recurrence_time     - (Required) The time each day when the schedule takes effect. Must match the format HHmm where HH is 00-23 and mm is 00-59 (e.g. 0930, 2300, etc.)
      enabled                   - (Optional) Whether to enable the schedule. Possible values are true and false. Defaults to true.
      timezone                  - (Optional) The time zone ID (e.g. Pacific Standard time). Refer to this guide for a full list of accepted time zone names: https://jackstromberg.com/2017/01/list-of-time-zones-consumed-by-azure/. Defaults to Central Europe Standard Time.
      notification_enabled      - (Optional) Whether to enable pre-shutdown notifications. Possible values are true and false. Defaults to false
      notification_time_in_mins - (Optional) Time in minutes between 15 and 120 before a shutdown event at which a notification will be sent. Defaults to 30.
      notification_webhook_url  - (Optional) The webhook URL to which the notification will be sent.
      notification_email        - (Optional) (Optional) E-mail address to which the notification will be sent.
  EOT
}

variable "vnic_ids" {
  type        = list(string)
  default     = []
  description = "(Optional) List of externally defined VNIC IDs"
}