variable "conventions" {
  description = "(Required) Access to terraform-conventions module."
  validation {
    condition     = length(var.conventions.project) > 0
    error_message = "Please provide value to the project in the conventions"
  }
}

variable "resource_group_name" {
  type        = string
  description = "(Required) Azure resource group name."
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix."

  validation {
    condition     = can(regex("^[a-zA-Z0-9-]{2,}$", var.resource_name_suffix))
    error_message = "Can contain alphanumeric characters and hyphen and should contain at least two characters"
  }
}

variable "subnet_id" {
  type        = string
  description = "(Required) Subnet for private endpoints."
}

variable "azurerm_service_plan_sku_name" {
  type        = string
  description = "(Optional) The SKU for the plan. Possible values include B1, B2, B3, D1, F1, I1, I2, I3, I1v2, I2v2, I3v2, P1v2, P2v2, P3v2, P1v3, P2v3, P3v3, S1, S2, S3, SHARED, EP1, EP2, EP3, WS1, WS2, WS3, and Y1."
  default     = null
}

variable "client_certificate_enabled" {
  type        = bool
  default     = null
  description = "(Optional) Should Client Certificates be enabled? Defaults to true when http2_enabled is false. Defaults to false when http2_enabled is true."
}

variable "client_certificate_mode" {
  type        = string
  default     = "Required"
  description = "(Optional) The Client Certificate mode. Possible values are Required, Optional, and OptionalInteractiveUser. This property has no effect when client_certificate_enabled is false. Defaults to Required."

    validation {
    condition     = contains(["Required", "Optional", "OptionalInteractiveUser"], var.client_certificate_mode)
    error_message = "Please, provide an authorized value! Possible values include Required, Optional, OptionalInteractiveUser."
  }
}

variable "https_only" {
  type        = bool
  default     = true
  description = "(Optional) Should the Linux Web App require HTTPS connections. Defaults to true. Policy exception is required to update this parameter."
}

variable "delegated_subnet_id" {
  type        = string
  description = "(Required) Delegated subnet for app service vnet integration"
}

variable "auth_settings" {
  type = object({
    enabled = bool
    active_directory = optional(object({
      client_id = string
      })
    )
    unauthenticated_client_action = optional(string)

  })

  default = {
    enabled = true
    active_directory = {
      client_id = ""
    }
    unauthenticated_client_action = "RedirectToLoginPage"

  }


  description = <<-EOT
  enabled - (Required) Should the Authentication / Authorization feature be enabled for the Linux Function App?
  active_directory - (Optional) An active_directory block as defined above.
  unauthenticated_client_action - (Optional) The action to take when an unauthenticated client attempts to access the app. Possible values include: RedirectToLoginPage, AllowAnonymous.
  EOT

}

variable "identity_ids" {
  type        = list(string)
  default     = null
  description = "(Optional) Specifies a list of User Assigned Managed Identity IDs to be assigned to this Windows Web App. A SystemAssigned identity will always be created."
}


variable "site_config" {
  type = object({
    vnet_route_all_enabled   = optional(bool,true)
    http2_enabled            = optional(bool,true)
    minimum_tls_version      = optional(string,"1.2")
    always_on                = optional(bool)
    health_check_path        = string
    health_check_eviction_time_in_min = optional(number)
    remote_debugging_enabled = optional(bool,false)
    container_registry_managed_identity_client_id = optional(string)
    container_registry_use_managed_identity       = optional(bool)    
    application_stack = optional(object({
      current_stack                = optional(string)
      dotnet_version               = optional(string)
      dotnet_core_version          = optional(string)
      tomcat_version               = optional(string)
      java_embedded_server_enabled = optional(string)
      java_version                 = optional(string)
      node_version                 = optional(string)
      php_version                  = optional(string)
      docker_image_name            = optional(string)
      docker_registry_url          = optional(string)
      docker_registry_username     = optional(string)
      docker_registry_password     = optional(string)      
      }
    ))
  })
  default = {
    minimum_tls_version      = "1.2"
    vnet_route_all_enabled   = true
    http2_enabled            = true
    health_check_path        = "/"
    remote_debugging_enabled = false
    
  }

  #validate health check path
  validation {
    condition     = var.site_config.health_check_path != null
    error_message = "health_check_path must be specified."
  }
  #validate dotnet version
  validation {
    condition     = var.site_config.application_stack.dotnet_version == "v7.0" || var.site_config.application_stack.dotnet_version == "v8.0" ||var.site_config.application_stack.dotnet_version == null
    error_message = "Dotnent version must be v7.0 or v8.0"
  }
  description = <<EOT
    (Required) A site_config block as defined below.
      always_on - (Optional) If this Windows Web App is Always On enabled. Defaults to true.
                  always_on must be explicitly set to false when using Free, F1, D1, or Shared Service Plans.
      vnet_route_all_enabled   - (Optional) Should all outbound traffic to have NAT Gateways, Network Security Groups and User Defined Routes applied? Defaults to true which is the policy requirement at OTP HU.
      http2_enabled            - (Optional) Should the HTTP2 be enabled? Defaults to true which is the policy requirement at OTP HU.
      minimum_tls_version      - (Optional) The configures the minimum version of TLS required for SSL requests. Possible values include: 1.0, 1.1, and 1.2. Defaults to 1.2 which is the policy requirement at OTP HU.
      remote_debugging_enabled - (Optional) Should Remote Debugging be enabled. Defaults to false which is the policy requirement at OTP HU.
      container_registry_managed_identity_client_id - (Optional) The Client ID of the Managed Service Identity to use for connections to the Azure Container Registry.
      container_registry_use_managed_identity       - (Optional) Should connections for Azure Container Registry use Managed Identity.
      health_check_path - (Optional) The path to the Health Check.
      health_check_eviction_time_in_min - (Optional) The amount of time in minutes that a node can be unhealthy before being removed from the load balancer. Possible values are between 2 and 10. Only valid in conjunction with health_check_path.
      An application_stack block supports the following:
        current_stack       - (Optional) The Application Stack for the Windows Web App. Possible values include dotnet, dotnetcore, node, python, php, and java.
        dotnet_version      - (Optional) The version of .NET to use when current_stack is set to dotnet. Possible values include v2.0,v3.0, v4.0, v5.0, v6.0, v7.0 and v8.0.
        dotnet_core_version - (Optional) The version of .NET to use when current_stack is set to dotnetcore. Possible values include v4.0.
        tomcat_version      - (Optional) The version of Tomcat the Java App should use. Conflicts with java_embedded_server_enabled
        java_embedded_server_enabled - (Optional) Should the Java Embedded Server (Java SE) be used to run the app.
        java_version        - (Optional) The version of Java to use when current_stack is set to java.
        node_version        - (Optional) The version of node to use when current_stack is set to node. Possible values are ~12, ~14, ~16, and ~18.
        php_version         - (Optional) The version of PHP to use when current_stack is set to php. Possible values are 7.1, 7.4 and Off.
        docker_image_name   - (Optional) The docker image, including tag, to be used. e.g. appsvc/staticsite:latest.
        docker_registry_url - (Optional) The URL of the container registry where the docker_image_name is located. e.g. https://index.docker.io or https://mcr.microsoft.com. This value is required with docker_image_name.
        docker_registry_username - (Optional) The user name to use for authentication against the registry to pull the image.
        docker_registry_password - (Optional) The password to use for authentication against the registry to pull the image. Do not pass password in clear text format.
  EOT
}

variable "dflt_app_settings" {
  type = object({
    WEBSITE_DNS_SERVER = string
  })
  default = {
    "WEBSITE_DNS_SERVER" : "**********"
  }
  description = "Mandatory settings for app_settings"
}

variable "app_settings" {
  type        = map(any)
  default     = {}
  description = "Custom app settings"
}

variable "worker_count" {
  type    = number
  default = 2
  validation {
    condition     = var.worker_count > 0
    error_message = "Worker count must be > 0."
  }
  description = "(Optional) The number of Workers (instances) to be allocated. Defaults to 2."
}

//Diagnostic settings
variable "log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace"
  type        = string
  default     = null
}

variable "log_analytics_diag_logs" {
  description = "(Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "log_analytics_metrics" {
  description = "(Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}

variable "resource_health_monitoring" {
  description = "(Optional) Create resource health monitoring alert rule. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null
}

variable "pe_wait_after" {
  type        = number
  description = "(Optional) Seconds to wait after private endpoint created."
  default     = 120
}

variable "windows_web_app_tags" {
  description = "(Optional) Tags to apply to the Windows Web App"
  type        = map(string)
  default     = null
}

variable "detailed_error_messages" {
  description = "(Optional) Should detailed error messages be enabled. Defaults to true"
  type        = bool
  default     = true
}

variable "failed_request_tracing" {
  description = "(Optional) Should tracing be enabled for failed requests. Defaults to true."
  type        = bool
  default     = true
}

variable "vnet_image_pull_enabled" {
  description = "(Optional) To enable pulling image over virtual network. Defaults to true to comply with Azure policies. "
  type        = bool
  default     = true
}

variable "vnet_content_share_enabled" {
description = "(Optional) To enable accessing content over virtual network. Defaults to true to comply with Azure policies."
  type        = bool
  default     = true

} 

variable "vnet_backup_restore_enabled" {
  description = "(Optional) To enable backup-restore over virtual network. Defaults to true to comply with Azure policies."
  type        = bool
  default     = true
}

variable "auth_settings_v2" {
  type = object({
    auth_enabled          = bool
    http_route_api_prefix = optional(string, "/.auth")
    active_directory_v2 = object({
      client_id                            = string
      client_secret_setting_name           = optional(string, "MICROSOFT_PROVIDER_AUTHENTICATION_SECRET")
      allowed_applications                 = optional(list(string), [])
      allowed_audiences                    = optional(list(string), [])
      allowed_groups                       = optional(list(string), [])
      allowed_identities                   = optional(list(string), [])
      client_secret_certificate_thumbprint = optional(string)
      jwt_allowed_client_applications      = optional(list(string), [])
      jwt_allowed_groups                   = optional(list(string), [])
      login_parameters                     = optional(map(any))
    })
    login = optional(object({
      allowed_external_redirect_urls = optional(list(string), [])
      token_store_enabled            = optional(bool, false)
      }),
      ({
        allowed_external_redirect_urls = []
        token_store_enabled            = false
      })
    )
    unauthenticated_action = optional(string, "RedirectToLoginPage")

  })
  default = null

  description = <<-EOT
  auth_enabled - (Required) Should the Authentication / Authorization feature be enabled for the Linux Function App?
  active_directory_v2 - (Optional) An active_directory block as defined below.
    client_id - (Required) The ID of the Client to use to authenticate with Azure Active Directory.
    client_secret_setting_name - (Optional) The App Setting name that contains the client secret of the Client.
    allowed_applications - (Optional) The list of allowed Applications for the Default Authorisation Policy.
    allowed_audiences - (Optional) Specifies a list of Allowed audience values to consider when validating JWTs issued by Azure Active Directory.
    allowed_groups - (Optional) The list of allowed Group Names for the Default Authorisation Policy.
    allowed_identities - (Optional) The list of allowed Identities for the Default Authorisation Policy.
    client_secret_certificate_thumbprint - (Optional) The thumbprint of the certificate used for signing purposes.
  unauthenticated_client_action - (Optional) The action to take when an unauthenticated client attempts to access the app. Possible values include: RedirectToLoginPage, AllowAnonymous.
  EOT  
}

variable "connection_strings" {
  type = list(object({
    name  = string
    type  = string
    value = string
  }))
  default = null
  description = <<EOT
    (Optional) One or more connection_string blocks as defined below.
      name - (Required) The name which should be used for this Connection.
      type - (Required) Type of database. Possible values include: APIHub, Custom, DocDb, EventHub, MySQL, NotificationHub, PostgreSQL, RedisCache, ServiceBus, SQLAzure, and SQLServer.
      value - (Required) The connection string value.
  EOT
}

variable "auth_version" {
  type        = string
  default     = "v1"
  description = "(Optional) Possible values are v1 or v2."

  validation {
    condition     = contains(["v1","v2"], var.auth_version)
    error_message = "Please, provide an authorized value! Possible values are v1 or v2. Thank you."
  }

}

variable "storage_accounts" {
  type = list(object({
    stac_object  = any
    share_name   = string
    type         = optional(string)
    mount_path   = optional(string)
  }))
  default = []
  description = <<EOT
  (Optional) List object to define one or more storage accounts to connect to web-app. Storage account must be created outside of web-app module.
    stac_object  - (Required) Storage account object what you would like to connect (storage account must have shared key enabled)
    share_name   - (Required) The Name of the File Share or Container Name for Blob storage
    type         - (Optional) The Azure Storage Type. Possible values include AzureFiles and AzureBlob. Defaults to AzureFiles.
    mount_path   - (Optional) The path at which to mount the storage share.
  EOT
  validation {
    condition     = length(var.storage_accounts) < 6
    error_message = "Can contain alphanumeric characters and hyphen and should contain at least two characters"
  }

}

variable "app_service_plan_id" {
  type        = string
  description = "(Optional) Allow using existing App Service plan instead of deploying a dedicated one for each web app. This is the RECOMMENDED way for new deployments."
  default     = null
}